<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\OpenApplication;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;

class CloseExpiredApplications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'applications:close-expired';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Close applications that have passed their closing date by setting active_status = 0 and application_status = 86';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting to close expired applications...');

        try {
            // Get current date
            $currentDate = Carbon::now()->format('Y-m-d');
            
            // Find applications that have passed their closing date and are still active
            $expiredApplications = OpenApplication::where('active_status', 1)
                ->where('application_status', 85) // Only close applications with status 85 (active)
                ->where(function ($query) use ($currentDate) {
                    $query->where('closing_date', '<', $currentDate)
                          ->orWhere(function ($subQuery) use ($currentDate) {
                              // Check extended deadline if extension is enabled
                              $subQuery->where('deadline_extented_status', 1)
                                       ->whereNotNull('deadline_extented_date')
                                       ->where('deadline_extented_date', '<', $currentDate);
                          });
                })
                ->get();

            if ($expiredApplications->isEmpty()) {
                $this->info('No expired applications found.');
                Log::info('CloseExpiredApplications: No expired applications found.');
                return 0;
            }

            $closedCount = 0;
            $errors = [];

            foreach ($expiredApplications as $application) {
                try {
                    // Determine which closing date to use
                    $effectiveClosingDate = $application->deadline_extented_status == 1 && $application->deadline_extented_date
                        ? $application->deadline_extented_date
                        : $application->closing_date;

                    // Update the application
                    $application->update([
                        'active_status' => 0,
                        'application_status' => 86,
                        'updated_emp' => 0, // System update
                        'updated_date' => Carbon::now(),
                    ]);

                    $closedCount++;
                    
                    $this->line("Closed application ID: {$application->id} - Course: {$application->course->course_name ?? 'N/A'} - Closing Date: {$effectiveClosingDate}");
                    
                    // Log the closure
                    Log::info("CloseExpiredApplications: Closed application", [
                        'application_id' => $application->id,
                        'course_code' => $application->course_code,
                        'course_name' => $application->course->course_name ?? 'N/A',
                        'display_name' => $application->display_name,
                        'original_closing_date' => $application->closing_date,
                        'extended_closing_date' => $application->deadline_extented_date,
                        'effective_closing_date' => $effectiveClosingDate,
                        'reg_year' => $application->reg_year,
                        'batch' => $application->batch,
                        'intake' => $application->intake,
                    ]);

                } catch (\Exception $e) {
                    $error = "Failed to close application ID: {$application->id} - Error: {$e->getMessage()}";
                    $errors[] = $error;
                    $this->error($error);
                    
                    Log::error("CloseExpiredApplications: Failed to close application", [
                        'application_id' => $application->id,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                }
            }

            // Summary
            $this->info("Successfully closed {$closedCount} expired applications.");
            
            if (!empty($errors)) {
                $this->error("Encountered " . count($errors) . " errors:");
                foreach ($errors as $error) {
                    $this->error("  - {$error}");
                }
            }

            // Log summary
            Log::info("CloseExpiredApplications: Command completed", [
                'total_found' => $expiredApplications->count(),
                'successfully_closed' => $closedCount,
                'errors_count' => count($errors),
                'execution_date' => $currentDate
            ]);

            return 0;

        } catch (\Exception $e) {
            $this->error("Command failed with error: {$e->getMessage()}");
            Log::error("CloseExpiredApplications: Command failed", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }
}
