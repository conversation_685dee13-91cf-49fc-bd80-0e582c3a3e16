<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\OpenApplication;
use Carbon\Carbon;

class TestCloseExpiredApplications extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'applications:test-close-expired {--dry-run : Show what would be closed without actually closing}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test the close expired applications functionality (dry run available)';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $isDryRun = $this->option('dry-run');
        
        if ($isDryRun) {
            $this->info('Running in DRY RUN mode - no changes will be made');
        } else {
            $this->info('Running in LIVE mode - applications will be closed');
        }

        $this->info('Testing close expired applications functionality...');

        try {
            // Get current date
            $currentDate = Carbon::now()->format('Y-m-d');
            $this->info("Current date: {$currentDate}");

            // Find applications that have passed their closing date and are still active
            $expiredApplications = OpenApplication::with('course')
                ->where('active_status', 1)
                ->where('application_status', 85) // Only close applications with status 85 (active)
                ->where(function ($query) use ($currentDate) {
                    $query->where('closing_date', '<', $currentDate)
                          ->orWhere(function ($subQuery) use ($currentDate) {
                              // Check extended deadline if extension is enabled
                              $subQuery->where('deadline_extented_status', 1)
                                       ->whereNotNull('deadline_extented_date')
                                       ->where('deadline_extented_date', '<', $currentDate);
                          });
                })
                ->get();

            if ($expiredApplications->isEmpty()) {
                $this->info('No expired applications found.');
                return 0;
            }

            $this->info("Found {$expiredApplications->count()} expired applications:");
            $this->newLine();

            // Create table headers
            $headers = ['ID', 'Course Name', 'Display Name', 'Closing Date', 'Extended Date', 'Effective Date', 'Status', 'App Status'];
            $rows = [];

            foreach ($expiredApplications as $application) {
                // Determine which closing date to use
                $effectiveClosingDate = $application->deadline_extented_status == 1 && $application->deadline_extented_date
                    ? $application->deadline_extented_date
                    : $application->closing_date;

                $rows[] = [
                    $application->id,
                    $application->course->course_name ?? 'N/A',
                    $application->display_name ?? 'N/A',
                    $application->closing_date,
                    $application->deadline_extented_date ?? 'N/A',
                    $effectiveClosingDate,
                    $application->active_status == 1 ? 'Active' : 'Inactive',
                    $application->application_status
                ];
            }

            $this->table($headers, $rows);

            if (!$isDryRun) {
                if ($this->confirm('Do you want to proceed with closing these applications?')) {
                    $closedCount = 0;
                    $errors = [];

                    foreach ($expiredApplications as $application) {
                        try {
                            $application->update([
                                'active_status' => 0,
                                'application_status' => 86,
                                'updated_emp' => 0, // System update
                                'updated_date' => Carbon::now(),
                            ]);

                            $closedCount++;
                            $this->info("✓ Closed application ID: {$application->id}");

                        } catch (\Exception $e) {
                            $error = "Failed to close application ID: {$application->id} - Error: {$e->getMessage()}";
                            $errors[] = $error;
                            $this->error("✗ {$error}");
                        }
                    }

                    $this->newLine();
                    $this->info("Successfully closed {$closedCount} expired applications.");
                    
                    if (!empty($errors)) {
                        $this->error("Encountered " . count($errors) . " errors.");
                    }
                } else {
                    $this->info('Operation cancelled.');
                }
            } else {
                $this->info('DRY RUN: These applications would be closed in live mode.');
            }

            return 0;

        } catch (\Exception $e) {
            $this->error("Command failed with error: {$e->getMessage()}");
            return 1;
        }
    }
}
