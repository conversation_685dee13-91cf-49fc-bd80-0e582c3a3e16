<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class <PERSON>el extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        // Close expired applications daily at 12:01 AM
        $schedule->command('applications:close-expired')
                 ->daily()
                 ->at('00:01')
                 ->withoutOverlapping()
                 ->runInBackground();

        // You can also run it multiple times a day if needed
        // $schedule->command('applications:close-expired')
        //          ->twiceDaily(1, 13) // Run at 1:00 AM and 1:00 PM
        //          ->withoutOverlapping()
        //          ->runInBackground();

        // Existing scheduled commands
        $schedule->command('queue:work --stop-when-empty')->everyMinute()->withoutOverlapping();
        $schedule->command('backup:clean')->daily()->at('01:00');
        $schedule->command('backup:run --only-db --disable-notifications')->daily()->at('01:30');
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
