<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Applicant;
use Carbon\Carbon;
use Illuminate\Support\Facades\Validator;

class PaymentSystemController extends Controller
{
    public function updatePayment(Request $request)
    {
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'student_payment_id' => 'required|numeric|not_in:0|exists:applicants,student_payment_id',
        ],[
            'student_payment_id.exists' => 'The student payment ID does not exist',
            'student_payment_id.not_in' => 'The student payment ID is invalid',
            'student_payment_id.numeric' => 'The student payment ID must be a number',
            'student_payment_id.required' => 'The student payment ID is required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation error',
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            // Find the applicant

            $applicant = Applicant::where('student_payment_id',$request->student_payment_id)->first();

            if (!$applicant) {
                return response()->json([
                    'success' => false,
                    'message' => 'Applicant not found'
                ], 404);
            }

            // Update payment information
            $applicant->application_fee_payment_status = 1;
            $applicant->application_fee_payment_date = Carbon::now();
            $applicant->submit_status = 3;
            $applicant->final_submit_date = Carbon::now();
            $applicant->save();

            return response()->json([
                'success' => true,
                'message' => 'Payment information updated successfully',
                'data' => $applicant->student_payment_id
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment information',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
