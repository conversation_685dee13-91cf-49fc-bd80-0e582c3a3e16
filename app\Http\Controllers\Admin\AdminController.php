<?php

namespace App\Http\Controllers\Admin;

use App\Models\User;
use Spatie\Permission\Models\Role;
use App\Http\Controllers\Controller;
use App\Mail\PasswordUpdateMail;
use App\Models\Applicant;
use App\Models\Course;
use App\Models\OpenApplication;
use App\Models\RegisteredStudent;
use App\Models\StudyBoard;
use App\Models\StudyBoardSubject;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class AdminController extends Controller implements HasMiddleware
{


    public static function middleware(): array
    {
        return [
            'auth'
        ];
    }

    public function index(){
        Gate::authorize('dashbord.view');

        $StudyBoards = StudyBoard::where('active_status',1)->count();
        $StudyBoardSubjects = StudyBoardSubject::where('active_status',1)->count();
        $ResearchCourses = Course::where('course_main_category',42)->where('active_status',1)->count();
        $TaughtCourses = Course::where('course_main_category',41)->where('active_status',1)->count();
        $registerdStudents = RegisteredStudent::where('active_status',1)->count();
        $registrationPendingStudent = Applicant::where('submit_status',3)->count();
        $applicationOpenCourses = OpenApplication::where('active_status',1)->count();
        $systemUsers = User::where('status',1)->count();


        return view('admin.index', compact('StudyBoards','StudyBoardSubjects','ResearchCourses','TaughtCourses','registerdStudents','registrationPendingStudent','applicationOpenCourses','systemUsers'));
    }

    public function ProfileView()
    {
        Gate::authorize('perofile.view');
        $id = Auth::user()->id;
        $user = User::find($id);
        $roles = Role::all();
        return view('admin.profile', compact('user', 'roles'));
    }

    public function ChangePasswordView(){

        $id = Auth()->user()->id;

        $user = User::find($id);

        return view('admin.change_password', compact('user'));
    }

    public function ChangePasswordUpdate(Request $request)
    {
        $validatedData = $request->validate([
            'oldpassword' => 'required',
            'password' => 'required|confirmed',
            'password_confirmation' => 'required'
        ], [
            'oldpassword.required' => 'The old password is required.',
            'password.required' => 'The new password is required.',
            'password.confirmed' => 'The new password and confirmation password do not match.',
            'password_confirmation.required' => 'The confirmation password is required.'
        ]);



        $hashedPassword = Auth()->user()->password;

        if (Hash::check($request->oldpassword, $hashedPassword)) {
            $user = User::find(Auth()->user()->id);
            $user->password = Hash::make($request->password);
            $user->password_change_status = 1;
            $user->save();

            Log::alert('AdminController -> ' . Auth()->user()->employee_no . 'password Changed');

            // Send password update confirmation email
            Mail::to($user->email)->send(new PasswordUpdateMail($user));

            Auth::logout();
            return redirect()->route('login');

        } else {

            $notification = array(
                'message' => 'User faild update password.Try Again',
                'alert-type' => 'error'
            );
            return redirect()->route('change.password.view')->with($notification);
        }
    }


}
