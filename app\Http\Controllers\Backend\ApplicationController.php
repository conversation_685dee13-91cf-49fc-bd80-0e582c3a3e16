<?php

namespace App\Http\Controllers\Backend;

use App\Http\Controllers\Controller;
use App\Http\Requests\ResearchApplicationOpenStoreRequest;
use App\Http\Requests\TaughtApplicationOpenStoreRequest;
use App\Models\Course;
use App\Models\CourseFees;
use App\Models\CourseResearchFee;
use App\Models\OpenApplication;
use App\Models\StudyBoardSubject;
use Illuminate\Routing\Controllers\HasMiddleware;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Auth;

class ApplicationController extends Controller implements HasMiddleware
{
    public static function middleware(): array
    {
        return [
            'auth',
        ];
    }

    public function applicationInitiateIndex()
    {
        Gate::authorize('application.initiate.list');

        $taughtCourseData = OpenApplication::with(['course.studyBoardName', 'course.courseCategory'])
            ->whereHas('course', function ($query) {
                $query->where('course_application_open_method', 44);
            })
            ->orderBy('open_applications.id', 'desc')
            ->select('open_applications.*')
            ->addSelect([
                'course_name' => Course::select('course_name')
                    ->whereColumn('open_applications.course_code', 'courses.id')
                    ->limit(1),
                'course_code' => Course::select('id')
                    ->whereColumn('open_applications.course_code', 'courses.id')
                    ->limit(1)
            ])
            ->get();

        //$researchCourseData = Course::whereHas('openApplications')->with('openApplications')->where('course_application_open_method', 43)->orderBy('id', 'desc')->get();

        $researchCourseData = OpenApplication::with(['course.studyBoardName', 'course.courseCategory'])
            ->whereHas('course', function ($query) {
                $query->where('course_application_open_method', 43);
            })
            ->where('reg_year', now()->year)
            ->orderBy('open_applications.id', 'desc')
            ->select('open_applications.*')
            ->addSelect([
                'course_name' => Course::select('course_name')
                    ->whereColumn('open_applications.course_code', 'courses.id')
                    ->limit(1),
                'course_code' => Course::select('id')
                    ->whereColumn('open_applications.course_code', 'courses.id')
                    ->limit(1)
            ])
            ->get();

        //dd($researchCourseData);

        return view('admin.application.initiate.index', compact('taughtCourseData', 'researchCourseData'));
    }

    public function applicationInitiateTaughtAdd()
    {
        Gate::authorize('application.initiate.taught.create');
        $courses = Course::where('active_status', 1)->where('course_application_open_method', 44)->get();
        return view('admin.application.initiate.add', compact('courses'));
    }

    public function applicationInitiateResearchAdd()
    {
        Gate::authorize('application.initiate.research.create');
        $courses = Course::with(['studyBoardName', 'courseCategory'])
            ->where('active_status', 1)
            ->where('course_application_open_method', 43)
            ->get();

        $currentYear = now()->year;

        // Check which courses already have applications for current year
        $existingApplications = OpenApplication::where('reg_year', $currentYear)
            ->whereIn('course_code', $courses->pluck('id'))
            ->pluck('course_code')
            ->toArray();

        // Add a flag to each course indicating if it's already initiated and load subjects
        $courses->each(function ($course) use ($existingApplications) {
            $course->is_initiated = in_array($course->id, $existingApplications);

            // Load subjects for PhD courses (course_cat_id == 20)
            if ($course->course_cat_id == 20) {
                $course->available_subjects = StudyBoardSubject::where('study_board_id', $course->study_board_id)
                    ->where('phd_eligiblity', 1)
                    ->where('active_status', 1)
                    ->get();
            } elseif ($course->course_cat_id == 21) {
                $course->available_subjects = StudyBoardSubject::where('study_board_id', $course->study_board_id)
                    ->where('mphil_eligiblity', 1)
                    ->where('active_status', 1)
                    ->get();
            } elseif ($course->course_cat_id == 22) {
                $course->available_subjects = StudyBoardSubject::where('study_board_id', $course->study_board_id)
                    ->where('mar_eligiblity', 1)
                    ->where('active_status', 1)
                    ->get();
            } elseif ($course->course_cat_id == 23) {
                $course->available_subjects = StudyBoardSubject::where('study_board_id', $course->study_board_id)
                    ->where('mat_eligiblity', 1)
                    ->where('active_status', 1)
                    ->get();
            } elseif ($course->course_cat_id == 24) {
                $course->available_subjects = StudyBoardSubject::where('study_board_id', $course->study_board_id)
                    ->where('maq_eligiblity', 1)
                    ->where('active_status', 1)
                    ->get();
            }

            else {
                $course->available_subjects = collect(); // Empty collection for non-PhD courses
            }
        });

        return view('admin.application.initiate.add_research', compact('courses', 'currentYear'));
    }

    public function applicationInitiateTaughtStore(TaughtApplicationOpenStoreRequest $request)
    {
        Gate::authorize('application.initiate.taught.create');

        $preparedData = $request->prepareData();

        foreach ($preparedData as $data) {
            OpenApplication::create($data);
        }

        $notification = array(
            'message' => 'New Taught Course Application Created Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('application.initiate.index')->with($notification);
    }

    public function applicationInitiateResearchStore(ResearchApplicationOpenStoreRequest $request)
    {
        Gate::authorize('application.initiate.research.create');

        $preparedData = $request->prepareData();

        foreach ($preparedData as $data) {
            OpenApplication::create($data);
        }

        $notification = array(
            'message' => 'New Research Course Application(s) Created Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('application.initiate.index')->with($notification);
    }

    public function applicationInitiateResearchQuick($courseId)
    {
        Gate::authorize('application.initiate.research.create');

        $course = Course::findOrFail($courseId);

        // Check if course is valid for research applications
        if ($course->course_application_open_method != 43) {
            $notification = array(
                'message' => 'This course is not configured for research applications',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }

        $currentYear = now()->year;
        $intake = 1;

        // Check if application already exists for this course and year
        $existingApplication = OpenApplication::where('course_code', $courseId)
            ->where('reg_year', $currentYear)
            ->first();

        if ($existingApplication) {
            $notification = array(
                'message' => 'Application for this course and academic year already exists',
                'alert-type' => 'warning'
            );
            return redirect()->back()->with($notification);
        }

        // Check if course research fees are configured for current year, batch 1, intake 1, and pay_income_type_id = 1
        $courseResearchFees = CourseResearchFee::where('course_code', $courseId)
            ->where('reg_year', $currentYear)
            ->where('batch', 1)
            ->where('intake', $intake)
            ->where('pay_income_type_id', 1)
            ->where('active_status', 1)
            ->exists();

        if (!$courseResearchFees) {
            $notification = array(
                'message' => 'Course research fees are not configured for this course (Year: ' . $currentYear . ', Batch: 1, Intake: ' . $intake . ', Income Type: 1). Please configure fees before initiating applications.',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }

        // Create the application record(s) based on course type
        $data = [];

        if ($course->course_cat_id == 20) {
            // For PhD courses - create records for each subject
            $studyBoardSubjects = StudyBoardSubject::where('study_board_id', $course->study_board_id)
                ->where('active_status', 1)
                ->where('phd_eligiblity', 1)
                ->get();

            foreach ($studyBoardSubjects as $subject) {
                $data[] = [
                    'course_code'  => $courseId,
                    'subject_code' => $subject->id,
                    'reg_year'     => $currentYear,
                    'batch'        => 1,
                    'intake'       => $intake,
                    'start_date'   => now()->startOfYear()->format('Y-m-d'),
                    'closing_date' => now()->endOfYear()->format('Y-m-d'),
                    'display_name' => 'PhD in ' . $subject->name,
                    'active_status' => 1,
                    'created_emp'  => Auth::user()->reg_no ?? 0,
                    'created_date' => now(),
                ];
            }
        } else if ($course->course_cat_id == 21) {
            // For MPhil courses - create records for each subject
            $studyBoardSubjects = StudyBoardSubject::where('study_board_id', $course->study_board_id)
                ->where('active_status', 1)
                ->where('mphil_eligiblity', 1)
                ->get();

            foreach ($studyBoardSubjects as $subject) {
                $data[] = [
                    'course_code'  => $courseId,
                    'subject_code' => $subject->id,
                    'reg_year'     => $currentYear,
                    'batch'        => 1,
                    'intake'       => $intake,
                    'start_date'   => now()->startOfYear()->format('Y-m-d'),
                    'closing_date' => now()->endOfYear()->format('Y-m-d'),
                    'display_name' => 'MPhil in ' . $subject->name,
                    'active_status' => 1,
                    'created_emp'  => Auth::user()->reg_no ?? 0,
                    'created_date' => now(),
                ];
            }
        } else if ($course->course_cat_id == 22) {
            // For MAR courses - create records for each subject
            $studyBoardSubjects = StudyBoardSubject::where('study_board_id', $course->study_board_id)
                ->where('active_status', 1)
                ->where('mar_eligiblity', 1)
                ->get();

            foreach ($studyBoardSubjects as $subject) {
                $data[] = [
                    'course_code'  => $courseId,
                    'subject_code' => $subject->id,
                    'reg_year'     => $currentYear,
                    'batch'        => 1,
                    'intake'       => $intake,
                    'start_date'   => now()->startOfYear()->format('Y-m-d'),
                    'closing_date' => now()->endOfYear()->format('Y-m-d'),
                    'display_name' => 'MAR in ' . $subject->name,
                    'active_status' => 1,
                    'created_emp'  => Auth::user()->reg_no ?? 0,
                    'created_date' => now(),
                ];
            }
        } else if ($course->course_cat_id == 23) {
            // For MAT courses - create records for each subject
            $studyBoardSubjects = StudyBoardSubject::where('study_board_id', $course->study_board_id)
                ->where('active_status', 1)
                ->where('mat_eligiblity', 1)
                ->get();

            foreach ($studyBoardSubjects as $subject) {
                $data[] = [
                    'course_code'  => $courseId,
                    'subject_code' => $subject->id,
                    'reg_year'     => $currentYear,
                    'batch'        => 1,
                    'intake'       => $intake,
                    'start_date'   => now()->startOfYear()->format('Y-m-d'),
                    'closing_date' => now()->endOfYear()->format('Y-m-d'),
                    'display_name' => 'MAT in ' . $subject->name,
                    'active_status' => 1,
                    'created_emp'  => Auth::user()->reg_no ?? 0,
                    'created_date' => now(),
                ];
            }
        } else if ($course->course_cat_id == 24) {      
            // For MAQ courses - create records for each subject
            $studyBoardSubjects = StudyBoardSubject::where('study_board_id', $course->study_board_id)
                ->where('active_status', 1)
                ->where('maq_eligiblity', 1)
                ->get();

            foreach ($studyBoardSubjects as $subject) {
                $data[] = [

        // Create the records
        foreach ($data as $record) {
            OpenApplication::create($record);
        }

        $notification = array(
            'message' => 'Research Course Application Initiated Successfully for ' . $course->course_name,
            'alert-type' => 'success'
        );

        return redirect()->back()->with($notification);
    }

    public function applicationInitiateEdit($id)
    {

        Gate::authorize('application.initiate.edit');

        $application = OpenApplication::with(['course.studyBoardName', 'course.courseCategory'])
            ->findOrFail($id);

        $courses = Course::where('active_status', 1)->where('course_application_open_method', 44)->get();

        return view('admin.application.initiate.edit', compact('application', 'courses'));
    }

    public function applicationInitiateShow($id)
    {
        Gate::authorize('application.initiate.show');

        $application = OpenApplication::findOrFail($id);

        $course = Course::findOrFail($application->course_code);

        if($course->course_main_category == 42){

            $payments = CourseResearchFee::where('course_code', $application->course_code)->where('reg_year', $application->reg_year)->where('batch', $application->batch)->get();

        }else {

            $payments = CourseFees::where('course_code', $application->course_code)->where('reg_year', $application->reg_year)->where('batch', $application->batch)->get();
        }



        return view('admin.application.initiate.show', compact('application', 'payments', 'course'));
    }

    public function applicationInitiateActive($id)
    {
        Gate::authorize('application.initiate.active');

        $application = OpenApplication::findOrFail($id);

        $course = Course::findOrFail($application->course_code);

        if($course->course_main_category == 42){

            $payments = CourseResearchFee::where('course_code', $application->course_code)->where('reg_year', $application->reg_year)->where('batch', $application->batch)->get();

        }else {

            $payments = CourseFees::where('course_code', $application->course_code)->where('reg_year', $application->reg_year)->where('batch', $application->batch)->get();
        }

        $totalPayment = $payments->sum('amount');

        if ($totalPayment <= 0) {
            $notification = array(
                'message' => 'Cannot activate application. Course Payment data not found.',
                'alert-type' => 'error'
            );
            return redirect()->back()->with($notification);
        }

        $application->update([
            'active_status' => 1
        ]);

        $notification = array(
            'message' => 'Application Activated Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('application.initiate.index')->with($notification);
    }

    public function applicationInitiateInactive($id)
    {
        Gate::authorize('application.initiate.inactive');

        $application = OpenApplication::findOrFail($id);

        $application->update([
            'active_status' => 0
        ]);

        $notification = array(
            'message' => 'Application Inactivated Successfully',
            'alert-type' => 'success'
        );

        return redirect()->route('application.initiate.index')->with($notification);
    }
}
