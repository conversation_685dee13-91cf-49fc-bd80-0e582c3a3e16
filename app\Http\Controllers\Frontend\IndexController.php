<?php

namespace App\Http\Controllers\Frontend;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Http\Requests\ApplicationDataSubmitRequest;
use App\Models\Applicant;
use App\Models\Category;
use App\Models\CourseFees;
use App\Models\CourseResearchFee;
use App\Models\EmployeeRecords;
use App\Models\Supervisor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Models\OpenApplication;
use App\Models\Qualification;
use App\Models\DocumentSummary;
use App\Models\StudyBoard;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Storage;
use Barryvdh\DomPDF\Facade\Pdf;

class IndexController extends Controller
{
    public function homePage()
    {
        return view('frontend.index');
    }

    public function openApplicationList()
    {
        return view('frontend.application_list');
    }

    public function openApplicationResearchList(Request $request)
    {
        $query = OpenApplication::with(['course.studyBoardName', 'course.courseCategory'])
            ->whereHas('course', function ($query) {
                $query->where('course_application_open_method', 43);
            })
            ->where('reg_year', now()->year);

        // Apply study board filter
        if ($request->filled('study_board_id')) {
            $query->whereHas('course', function ($q) use ($request) {
                $q->where('study_board_id', $request->study_board_id);
            });
        }

        // Apply course category filter
        if ($request->filled('course_category_id')) {
            $query->whereHas('course', function ($q) use ($request) {
                $q->where('course_cat_id', $request->course_category_id);
            });
        }

        $researchCourseData = $query->orderBy('open_applications.id', 'desc')
            ->select('open_applications.*')
            ->addSelect([
                'course_name' => Course::select('course_name')
                    ->whereColumn('open_applications.course_code', 'courses.id')
                    ->limit(1),
                'course_code' => Course::select('id')
                    ->whereColumn('open_applications.course_code', 'courses.id')
                    ->limit(1)
            ])
            ->whereDate('closing_date', '>', now()->format('Y-m-d'))
            ->where('application_status', 85)
            ->where('active_status', 1)
            ->get();

        // Get filter options
        $studyBoards = StudyBoard::where('active_status', 1)->orderBy('id')->get();

        $courseCategories = Category::where('category_type_id', 4)->whereIn('id', [20, 21, 22, 23, 24])->orderBy('id')->get();

        return view('frontend.research_application_list', compact('researchCourseData', 'studyBoards', 'courseCategories'));
    }

    public function openApplicationTaughtList()
    {
        $taughtCourseData = OpenApplication::with(['course.studyBoardName', 'course.courseCategory'])
            ->whereHas('course', function ($query) {
                $query->where('course_application_open_method', 44);
            })
            ->where('active_status', 1)
            ->whereDate('closing_date', '>', now()->format('Y-m-d'))
            ->where('application_status', 85)
            ->orderBy('open_applications.id', 'desc')
            ->select('open_applications.*')
            ->addSelect([
                'course_name' => Course::select('course_name')
                    ->whereColumn('open_applications.course_code', 'courses.id')
                    ->limit(1),
                'course_code' => Course::select('id')
                    ->whereColumn('open_applications.course_code', 'courses.id')
                    ->limit(1)
            ])
            ->get();

        return view('frontend.taught_application_list', compact('taughtCourseData'));
    }

    public function applicationRegistrationForm($id)
    {
        $id = decrypt($id);
        $course = OpenApplication::with(['course.studyBoardName', 'course.courseCategory'])->findOrFail($id);
        return view('frontend.taught_application_auth', compact('course'));
    }

    public function sendTaughtOtp(Request $request)
    {
        // Validate input based on citizenship type
        $validationRules = [
            'citizenship' => 'required',
            'mobile' => 'required',
            'email' => 'required|email',
        ];

        $request->validate($validationRules);


        $appCheck = Applicant::where('open_application_id', $request->course_id)
            ->where('email', $request->email)
            ->count();

        if ($appCheck > 0) {

            $notification = array(
                'message' => 'You have already applied for this course. Please use edit and submit option.',
                'alert-type' => 'error'
            );

            return redirect()->route('home')->with($notification);

        }


        // Generate OTP
        $otp = rand(10000, 99999);

        // Prepare session data
        $sessionData = [
            'taught_otp' => $otp,
            'taught_email' => $request->email,
            'taught_mobile' => $request->mobile,
            'taught_course_id' => $request->course_id,
            'taught_citizenship_type' => $request->citizenship,
        ];

        // Add identification based on citizenship type
        if ($request->citizenship === '59') {
            $sessionData['taught_nic'] = $request->nic;
        } else {
            $sessionData['taught_passport_number'] = $request->passport_number;
        }

        // Store data in session
        session($sessionData);

        // Send OTP to email
        Mail::to($request->email)->send(new \App\Mail\OtpMail($otp));

        return redirect()->route('otp.screen');
    }

    public function otpScreen()
    {
        // Base required session data
        $requiredSessions = ['taught_otp', 'taught_email', 'taught_mobile', 'taught_course_id', 'taught_citizenship_type'];

        // Add identification requirement based on citizenship type
        if (session('taught_citizenship_type') === '59') {
            $requiredSessions[] = 'taught_nic';
        } else {
            $requiredSessions[] = 'taught_passport_number';
        }

        // Check if all required session data exists
        foreach ($requiredSessions as $session) {
            if (!session()->has($session)) {

                $notification = array(
                    'message' => 'Session data expired. Please try again..',
                    'alert-type' => 'error'
                );

                return redirect()->route('active.application.taught.list')->with($notification);

            }
        }

        // Prepare user details
        $userDetail = [
            'otp' => session('taught_otp'),
            'email' => session('taught_email'),
            'mobile' => session('taught_mobile'),
            'course_id' => session('taught_course_id'),
            'citizenship_type' => session('taught_citizenship_type')
        ];

        // Add identification based on citizenship type
        if (session('taught_citizenship_type') === '59') {
            $userDetail['nic'] = session('taught_nic');
        } else {
            $userDetail['passport_number'] = session('taught_passport_number');
        }

        Log::info('OTP Screen accessed with user details:', $userDetail);
        return view('frontend.taught_application_otp', compact('userDetail'));
    }

    public function verifyTaughtOtp(Request $request)
    {
        $request->validate([
            'otp' => 'required|numeric',
        ]);

        if ($request->otp == session('taught_otp')) {
            // OTP verified, proceed to application form or next step
            $courseId = session('taught_course_id');

            if (!$courseId) {
                $notification = array(
                    'message' => 'Course ID missing in session. Please try again.',
                    'alert-type' => 'error'
                );

                return redirect()->back()->with($notification);

            }
            // Prepare session keys to forget
            $sessionKeys = [
                'taught_otp',
                'taught_email',
                'taught_mobile',
                'taught_course_id',
                'taught_citizenship_type'
            ];

            // Add identification key based on citizenship type
            if (session('taught_citizenship_type') === '59') {
                $sessionKeys[] = 'taught_nic';
            } else {
                $sessionKeys[] = 'taught_passport_number';
            }

            $taughtEmail = session('taught_email');
            $taughtMobile = session('taught_mobile');
            $citizenshipType = session('taught_citizenship_type');
            if ($citizenshipType === '59') {
                $identification = session('taught_nic');
            } else {
                $identification = session('taught_passport_number');
            }


            $application = new Applicant();
            $application->open_application_id = $courseId;
            $application->reference_no = $this->genrateRefNumber($courseId);
            $application->citizenship_type = $citizenshipType;
            if ($citizenshipType == 59) {
                //get nic information
                $empDetails = Http::withHeaders([
                    'Content-Type' => 'application/json',
                ])->post('https://hrms.sjp.ac.lk/api/nic/data', ['nic' => strtoupper($identification)]);

                $nicData = json_decode($empDetails->body(), true);

                $application->old_nic = $nicData['oldnic'] ?? null;
                $application->new_nic = $nicData['newnic'] ?? null;
                $application->active_nic = $nicData['activenic'] ?? null;
                $application->dob = $nicData['dob'] ?? null;
            } else {
                $application->passport = $identification;
            }
            $application->email = $taughtEmail;
            $application->mobile_no = $taughtMobile;
            $application->submit_status = 1;
            $application->applicant_status = 82;
            $application->save();

            // Store verification status and user details in session
            session([
                'otp_verified' => true,
                'application_email' => $taughtEmail,
                'application_mobile' => $taughtMobile,
                'citizenship_type' => $citizenshipType,
                'identification' => $identification,
            ]);

            // Clear all taught-related session data before redirect
            session()->forget($sessionKeys);

             $notification = array(
                    'message' => 'OTP verified successfully.',
                    'alert-type' => 'success'
                );

            return redirect()->route('application.show', [
                'openAppID' => encrypt($courseId),
                'id' => encrypt($application->id),
            ])->with($notification);
        }

        $notification = array(
            'message' => 'Invalid OTP. Please try again.',
            'alert-type' => 'error'
        );

        return redirect()->back()->with($notification);
    }

    private function genrateRefNumber($courseId)
    {
        // Pad courseId to at least 3 characters with leading zeros
        $coursePart = str_pad($courseId, 3, '0', STR_PAD_LEFT);

        // Get the max id for applicants for this course
        $maxAppId = \App\Models\Applicant::where('open_application_id', $courseId)->max('id');
        $nextAppId = $maxAppId ? $maxAppId + 1 : 1;
        $appPart = str_pad($nextAppId, 4, '0', STR_PAD_LEFT);

        return $coursePart . '/' . $appPart;
    }

    public function applicantionEditLogin()
    {

        return view('frontend.taught_application_edit');
    }

    public function applicationEditSendOtp(Request $request)
    {
        // Validate input based on citizenship type
        $validationRules = [
            'citizenship' => 'required',
            'mobile' => 'required',
            'email' => 'required|email',
        ];

        $request->validate($validationRules);

        $verifiedUser = Applicant::where('email', $request->email)->count();

        if ($verifiedUser == 0) {

            $notification = array(
                'message' => 'Could not find any records associated with your email.',
                'alert-type' => 'error'
            );

            return redirect()->route('applicantion.edit.login')->with($notification);
        }

        // Generate OTP
        $otp = rand(10000, 99999);

        // Prepare session data
        $sessionData = [
            'taught_otp' => $otp,
            'taught_email' => $request->email,
            'taught_mobile' => $request->mobile,
            'taught_citizenship_type' => $request->citizenship,
        ];

        // Add identification based on citizenship type
        if ($request->citizenship === '59') {
            $sessionData['taught_nic'] = $request->nic;
        } else {
            $sessionData['taught_passport_number'] = $request->passport_number;
        }

        // Store data in session
        session($sessionData);

        // Send OTP to email
        Mail::to($request->email)->send(new \App\Mail\OtpMail($otp));

        return redirect()->route('edit.otp.screen');
    }

    public function editOtpScreen()
    {

        // Base required session data
        $requiredSessions = ['taught_otp', 'taught_email', 'taught_mobile', 'taught_citizenship_type'];

        // Add identification requirement based on citizenship type
        if (session('taught_citizenship_type') === '59') {
            $requiredSessions[] = 'taught_nic';
        } else {
            $requiredSessions[] = 'taught_passport_number';
        }

        //dd($requiredSessions);

        // Check if all required session data exists
        foreach ($requiredSessions as $session) {
            if (!session()->has($session)) {

                $notification = array(
                    'message' => 'Session data expired. Please try again..',
                    'alert-type' => 'error'
                );

                return redirect()->route('applicantion.edit.login')->with($notification);
            }
        }

        // Prepare user details
        $userDetail = [
            'otp' => session('taught_otp'),
            'email' => session('taught_email'),
            'mobile' => session('taught_mobile'),
            'citizenship_type' => session('taught_citizenship_type')
        ];

        // Add identification based on citizenship type
        if (session('taught_citizenship_type') === '59') {
            $userDetail['nic'] = session('taught_nic');
        } else {
            $userDetail['passport_number'] = session('taught_passport_number');
        }

        Log::info('OTP Screen accessed with user details:', $userDetail);

        return view('frontend.taught_application_otp_edit', compact('userDetail'));
    }

    public function verifyEditTaughtOtp(Request $request)
    {
        $request->validate([
            'otp' => 'required|numeric',
        ]);

        if ($request->otp == session('taught_otp')) {


            // Prepare session keys to forget
            $sessionKeys = [
                'taught_otp',
                'taught_email',
                'taught_mobile',
                'taught_citizenship_type'
            ];

            // Add identification key based on citizenship type
            if (session('taught_citizenship_type') === '59') {
                $sessionKeys[] = 'taught_nic';
            } else {
                $sessionKeys[] = 'taught_passport_number';
            }

            $taughtEmail = session('taught_email');
            $taughtMobile = session('taught_mobile');
            $citizenshipType = session('taught_citizenship_type');
            if ($citizenshipType === '59') {
                $identification = session('taught_nic');
            } else {
                $identification = session('taught_passport_number');
            }

            // Store verification status and user details in session
            session([
                'otp_verified' => true,
                'application_email' => $taughtEmail,
                'application_mobile' => $taughtMobile,
                'citizenship_type' => $citizenshipType,
                'identification' => $identification,
            ]);

            // Clear all taught-related session data before redirect
            session()->forget($sessionKeys);

            $notification = array(
                'message' => 'OTP verified successfully.',
                'alert-type' => 'success'
            );

            return redirect()->route('enroll.list')->with($notification);
        }

        $notification = array(
            'message' => 'Invalid OTP. Please try again.',
            'alert-type' => 'error'
        );

        return redirect()->back()->with($notification);
    }

    public function enrollList()
    {
        $email = session()->get('application_email');

        $applicationList =  Applicant::join('open_applications', 'applicants.open_application_id', '=', 'open_applications.id')
            ->join('courses', 'open_applications.course_code', '=', 'courses.id')
            ->select('applicants.id as applicant_id', 'applicants.reference_no', 'applicants.submit_status', 'open_applications.closing_date', 'open_applications.display_name', 'open_applications.id as open_application_id','open_applications.active_status as application_status_open_app','courses.course_application_open_method','open_applications.application_status')
            ->where('open_applications.active_status', 1)
            //->where('open_applications.active_status', 1)
            ->whereIn('application_status', [85, 86])
            ->where('applicants.email', $email)->get();

        return view('frontend.application_edit_list', compact('applicationList'));
    }


    public function applicationShow($id, $applicantId)
    {
        if (!session('otp_verified')) {
            $notification = array(
                'message' => 'Please complete the verification process first.',
                'alert-type' => 'error'
            );

            return redirect()->route('home')->with($notification);
        }

        $CourseOpenId = decrypt($id);
        $applicationId = decrypt($applicantId);

        $courseData = OpenApplication::join('courses', 'open_applications.course_code', '=', 'courses.id')
            ->select('open_applications.*', 'courses.course_main_category')
            ->where('open_applications.id', $CourseOpenId)->firstOrFail();

        $categories = $this->getCategories([9, 10, 13, 14, 15]);
        $titles = $categories->where('category_type_id', '9');
        $genders = $categories->where('category_type_id', '10');
        $civilStatuses = $categories->where('category_type_id', '13');
        $qualificationTypes = $categories->where('category_type_id', '14');
        $degreeClassTypes = $categories->where('category_type_id', '15');
        $application = Applicant::find($applicationId);

        // Check if application exists and has submit_status = 1 (saved but not submitted)
        if (!$application || $application->submit_status != 1) {
            $notification = array(
                'message' => 'Application not found or not available for editing. Only saved applications can be edited.',
                'alert-type' => 'error'
            );

            return redirect()->route('enroll.list')->with($notification);
        }

        $qualifications = Qualification::where('applicant_id', $applicationId)->get();
        $employeeRecords = EmployeeRecords::where('applicant_id', $applicationId)->get();

        // Load existing documents
        $existingDocuments = DocumentSummary::where('applicant_id', $applicationId)->get()->keyBy('document_type');

        // Set current applicant ID in session for document security
        session(['current_applicant_id' => $applicationId]);

        // Load supervisor data for research courses
        $firstSupervisor = null;
        $secondSupervisor = null;
        if ($courseData->course_main_category == 42) {
            $firstSupervisor = Supervisor::where('applicant_id', $applicationId)
                                        ->where('supervisor_type', 1)
                                        ->first();
            $secondSupervisor = Supervisor::where('applicant_id', $applicationId)
                                         ->where('supervisor_type', 2)
                                         ->first();
        }

        return view('frontend.application.index', compact('courseData', 'CourseOpenId', 'titles', 'genders', 'applicationId', 'civilStatuses', 'qualificationTypes', 'degreeClassTypes', 'application', 'qualifications', 'employeeRecords', 'firstSupervisor', 'secondSupervisor', 'existingDocuments'));
    }



    private function handleFileUpload($file, $applicantId, $courseId, $documentType, $documentCategoryId = null, $documentTypeName = null)
    {
        if (!$file) {
            return null;
        }

        // Check if there's an existing document of this type for this applicant
        $existingDocument = DocumentSummary::where('applicant_id', $applicantId)
                                          ->where('document_type', $documentType)
                                          ->first();

        // If existing document found, delete the file and database record
        if ($existingDocument) {
            Log::info("Replacing existing document", [
                'applicant_id' => $applicantId,
                'document_type' => $documentType,
                'old_file' => $existingDocument->file_path,
                'old_filename' => $existingDocument->original_file_name
            ]);

            // Delete the physical file from storage
            if (Storage::disk('public')->exists($existingDocument->file_path)) {
                Storage::disk('public')->delete($existingDocument->file_path);

                // Clean up empty directories
                $this->cleanupEmptyDirectories($existingDocument->file_path);
            }

            // Delete the database record
            $existingDocument->delete();
        }

        $folderPath = "uploads/{$courseId}/{$applicantId}/{$documentTypeName}";

        // Generate unique filename
        $originalName = $file->getClientOriginalName();
        $extension = $file->getClientOriginalExtension();
        $filename = time() . '_' . uniqid() . '.' . $extension;

        // Store file in public disk
        $filePath = $file->storeAs($folderPath, $filename, 'public');

        // Save to document_summaries table
        DocumentSummary::create([
            'applicant_id' => $applicantId,
            'document_type' => $documentType,
            'document_category_id' => $documentCategoryId,
            'file_path' => $filePath,
            'original_file_name' => $originalName
        ]);

        return $filePath;
    }


    private function cleanupEmptyDirectories($filePath)
    {
        $directory = dirname($filePath);

        // Check if directory exists and is empty
        if (Storage::disk('public')->exists($directory)) {
            $files = Storage::disk('public')->files($directory);
            $directories = Storage::disk('public')->directories($directory);

            // If directory is empty (no files and no subdirectories), delete it
            if (empty($files) && empty($directories)) {
                Storage::disk('public')->deleteDirectory($directory);

                // Recursively check parent directories
                $parentDirectory = dirname($directory);
                if ($parentDirectory !== $directory && $parentDirectory !== '.') {
                    $this->cleanupEmptyDirectories($parentDirectory);
                }
            }
        }
    }

    public function ApplicationDataSubmit(ApplicationDataSubmitRequest $request)
    {

        if ($request->has('save')) {

            $application = Applicant::find($request->input('applicant_id'));
            $application->title_id = $request->input('title_id');
            $application->initials = $request->input('initials');
            $application->name_denoted_by_initials = $request->input('name_denoted_by_initials');
            $application->last_name = $request->input('last_name');
            $application->dob = $request->input('dob');
            $application->gender_cat_id = $request->input('gender_cat_id');
            $application->civil_status_cat_id = $request->input('civil_status_cat_id');
            $application->permanent_add1 = $request->input('permanent_address1');
            $application->permanent_add2 = $request->input('permanent_address2');
            $application->permanent_add3 = $request->input('permanent_address3');
            $application->permanent_city_id = $request->input('permanent_city');
            $application->permanent_postal_code = $request->input('permanent_postal_code');
            $application->postal_add1 = $request->input('postal_address1');
            $application->postal_add2 = $request->input('postal_address2');
            $application->postal_add3 = $request->input('postal_address3');
            $application->postal_city_id = $request->input('postal_city');
            $application->postal_postal_code = $request->input('postal_postal_code');
            $application->mobile_no = $request->input('mobile_no');
            $application->email = $request->input('email');
            $application->tel_home = $request->input('tel_home');
            $application->tel_office = $request->input('tel_office');

            $application->first_degree_name = $request->input('first_degree_name');
            $application->major_subject = $request->input('first_degree_major_subject');
            $application->university = $request->input('first_degree_university');
            $application->class_cat_id = $request->input('class_cat_id');
            $application->effective_date = $request->input('first_degree_effective_date');
            $application->duration = $request->input('first_degree_duration');
            $application->gpa = $request->input('gpa');

            if ($request->has('qualifications') && is_array($request->input('qualifications'))) {
                foreach ($request->input('qualifications') as $qualificationData) {
                    $qualification = new Qualification();
                    $qualification->applicant_id = $application->id;
                    $qualification->qualification_type_id = $qualificationData['qualification_type_id'];
                    $qualification->qulification_name = $qualificationData['qulification_name'];
                    $qualification->university = $qualificationData['qualification_university'];
                    $qualification->effective_date = $qualificationData['qualification_effective_date'];
                    $qualification->duration = $qualificationData['qualification_duration'];
                    $qualification->major_subject = $qualificationData['qualification_major_subject'];
                    $qualification->save();
                }
            }

            // employment records
            if ($request->has('employment_records') && is_array($request->input('employment_records'))) {
                foreach ($request->input('employment_records') as $employmentData) {
                    $employmentRecord = new EmployeeRecords();
                    $employmentRecord->applicant_id = $application->id;
                    $employmentRecord->working_status = $employmentData['working_status'];
                    $employmentRecord->employer = $employmentData['institution'];
                    $employmentRecord->address = $employmentData['emp_address'];
                    $employmentRecord->position = $employmentData['position'];
                    $employmentRecord->period = $employmentData['period'];
                    $employmentRecord->nature_of_duty = $employmentData['nature_of_duty'];
                    $employmentRecord->save();
                }
            }

            // Handle research course
            if ($request->input('course_main_category') == 42) {

                $application->proposal_title = $request->input('proposal_title');
                $application->research_summary = $request->input('research_summary');

                // Handle First Supervisor
                if ($request->filled('supervisor1_name') || $request->filled('supervisor1_email') || $request->filled('supervisor1_address') || $request->filled('supervisor1_mobile') || $request->filled('supervisor1_tel_home') || $request->filled('supervisor1_tel_office')) {

                    Supervisor::where('applicant_id', $application->id)->where('supervisor_type', 1)->delete();

                    $supervisor1 = new Supervisor();
                    $supervisor1->applicant_id = $application->id;
                    $supervisor1->supervisor_type = 1;
                    $supervisor1->full_name = $request->input('supervisor1_name');
                    $supervisor1->email = $request->input('supervisor1_email');
                    $supervisor1->address = $request->input('supervisor1_address');
                    $supervisor1->mobile_no = $request->input('supervisor1_mobile');
                    $supervisor1->tel_home = $request->input('supervisor1_tel_home');
                    $supervisor1->tel_office = $request->input('supervisor1_tel_office');
                    $supervisor1->save();
                }


                if ($request->filled('supervisor2_name') || $request->filled('supervisor2_email') || $request->filled('supervisor2_address') || $request->filled('supervisor2_mobile') || $request->filled('supervisor2_tel_home') || $request->filled('supervisor2_tel_office')) {

                    Supervisor::where('applicant_id', $application->id)->where('supervisor_type', 2)->delete();

                    $supervisor2 = new Supervisor();
                    $supervisor2->applicant_id = $application->id;
                    $supervisor2->supervisor_type = 2;
                    $supervisor2->full_name = $request->input('supervisor2_name');
                    $supervisor2->email = $request->input('supervisor2_email');
                    $supervisor2->address = $request->input('supervisor2_address');
                    $supervisor2->mobile_no = $request->input('supervisor2_mobile');
                    $supervisor2->tel_home = $request->input('supervisor2_tel_home');
                    $supervisor2->tel_office = $request->input('supervisor2_tel_office');
                    $supervisor2->save();
                }
            }


                $application->ref_name1 = $request->input('referee1_name');
                $application->ref_email1 = $request->input('referee1_email');
                $application->ref_mobile1 = $request->input('referee1_phone');

                $application->ref_name2 = $request->input('referee2_name');
                $application->ref_email2 = $request->input('referee2_email');
                $application->ref_mobile2 = $request->input('referee2_phone');

            // Handle file uploads and save to document_summaries table
            $courseId = $application->open_application_id;
            $applicantId = $application->id;

            // Upload profile photo
            if ($request->hasFile('profile_photo')) {
                $this->handleFileUpload(
                    $request->file('profile_photo'),
                    $applicantId,
                    $courseId,
                    1, // Profile Photo
                    73,  // Document category ID
                    'Profile Photo'  // Document type name
                );
            }

            // Upload degree certificate
            if ($request->hasFile('degree_certificate')) {
                $this->handleFileUpload(
                    $request->file('degree_certificate'),
                    $applicantId,
                    $courseId,
                    2, // Degree Certificate
                    74,  // Document category ID
                    'Degree Certificate'  // Document category ID
                );
            }

            // Upload degree transcript
            if ($request->hasFile('degree_transcript')) {
                $this->handleFileUpload(
                    $request->file('degree_transcript'),
                    $applicantId,
                    $courseId,
                    3, // Degree Transcript
                    75,  // Document category ID
                    'Degree Transcript'  // Document type name
                );
            }

            // Upload birth certificate
            if ($request->hasFile('birth_certificate')) {
                $this->handleFileUpload(
                    $request->file('birth_certificate'),
                    $applicantId,
                    $courseId,
                    4, // Birth Certificate
                    76,  // Document category ID
                    'Birth Certificate'  // Document category ID
                );
            }

            // Upload NIC/Passport document
            if ($request->hasFile('nic_passport_doc')) {
                $this->handleFileUpload(
                    $request->file('nic_passport_doc'),
                    $applicantId,
                    $courseId,
                    5, // NIC/Passport
                    77,  // Document category ID
                    'Identity Document'  // Document type name
                );
            }

            // Upload research proposals (only for research courses)
            if ($request->input('course_main_category') == 42) {
                if ($request->hasFile('research_proposal1')) {
                    $this->handleFileUpload(
                        $request->file('research_proposal1'),
                        $applicantId,
                        $courseId,
                        6, // Research Proposal 1
                        78,  // Document category ID
                        'Research Proposal 1'  // Document type name
                    );
                }

                if ($request->hasFile('research_proposal2')) {
                    $this->handleFileUpload(
                        $request->file('research_proposal2'),
                        $applicantId,
                        $courseId,
                        7, // Research Proposal 2
                        79,  // Document category ID
                        'Research Proposal 2'  // Document type name
                    );
                }
            }


            $application->submit_status = 1;
            $application->save();

            return redirect()->back()->with('success', 'Application saved successfully!');

        } else if ($request->has('submit')) {

            $application = Applicant::find($request->input('applicant_id'));
            $application->title_id = $request->input('title_id');
            $application->initials = $request->input('initials');
            $application->name_denoted_by_initials = $request->input('name_denoted_by_initials');
            $application->last_name = $request->input('last_name');
            $application->dob = $request->input('dob');
            $application->gender_cat_id = $request->input('gender_cat_id');
            $application->civil_status_cat_id = $request->input('civil_status_cat_id');
            $application->permanent_add1 = $request->input('permanent_address1');
            $application->permanent_add2 = $request->input('permanent_address2');
            $application->permanent_add3 = $request->input('permanent_address3');
            $application->permanent_city_id = $request->input('permanent_city');
            $application->permanent_postal_code = $request->input('permanent_postal_code');
            $application->postal_add1 = $request->input('postal_address1');
            $application->postal_add2 = $request->input('postal_address2');
            $application->postal_add3 = $request->input('postal_address3');
            $application->postal_city_id = $request->input('postal_city');
            $application->postal_postal_code = $request->input('postal_postal_code');
            $application->mobile_no = $request->input('mobile_no');
            $application->email = $request->input('email');
            $application->tel_home = $request->input('tel_home');
            $application->tel_office = $request->input('tel_office');

            $application->first_degree_name = $request->input('first_degree_name');
            $application->major_subject = $request->input('first_degree_major_subject');
            $application->university = $request->input('first_degree_university');
            $application->class_cat_id = $request->input('class_cat_id');
            $application->effective_date = $request->input('first_degree_effective_date');
            $application->duration = $request->input('first_degree_duration');
            $application->gpa = $request->input('gpa');


            // Handle qualifications
            if ($request->has('qualifications') && is_array($request->input('qualifications'))) {
                foreach ($request->input('qualifications') as $qualificationData) {
                    $qualification = new Qualification();
                    $qualification->applicant_id = $application->id;
                    $qualification->qualification_type_id = $qualificationData['qualification_type_id'];
                    $qualification->qulification_name = $qualificationData['qulification_name'];
                    $qualification->university = $qualificationData['qualification_university'];
                    $qualification->effective_date = $qualificationData['qualification_effective_date'];
                    $qualification->duration = $qualificationData['qualification_duration'];
                    $qualification->major_subject = $qualificationData['qualification_major_subject'];
                    $qualification->save();
                }
            }

            // employment records
            if ($request->has('employment_records') && is_array($request->input('employment_records'))) {
                foreach ($request->input('employment_records') as $employmentData) {
                    $employmentRecord = new EmployeeRecords();
                    $employmentRecord->applicant_id = $application->id;
                    $employmentRecord->working_status = $employmentData['working_status'];
                    $employmentRecord->employer = $employmentData['institution'];
                    $employmentRecord->address = $employmentData['emp_address'];
                    $employmentRecord->position = $employmentData['position'];
                    $employmentRecord->period = $employmentData['period'];
                    $employmentRecord->nature_of_duty = $employmentData['nature_of_duty'];
                    $employmentRecord->save();
                }
            }


            if ($request->input('course_main_category') == 42) {

                $application->proposal_title = $request->input('proposal_title');
                $application->research_summary = $request->input('research_summary');

                // Handle First Supervisor
                if ($request->filled('supervisor1_name') && $request->filled('supervisor1_email') && $request->filled('supervisor1_address') && $request->filled('supervisor1_mobile')) {

                    Supervisor::where('applicant_id', $application->id)->where('supervisor_type', 1)->delete();

                    $supervisor1 = new Supervisor();
                    $supervisor1->applicant_id = $application->id;
                    $supervisor1->supervisor_type = 1;
                    $supervisor1->full_name = $request->input('supervisor1_name');
                    $supervisor1->email = $request->input('supervisor1_email');
                    $supervisor1->address = $request->input('supervisor1_address');
                    $supervisor1->mobile_no = $request->input('supervisor1_mobile');
                    $supervisor1->tel_home = $request->input('supervisor1_tel_home');
                    $supervisor1->tel_office = $request->input('supervisor1_tel_office');
                    $supervisor1->save();
                }


                if ($request->filled('supervisor2_name') && $request->filled('supervisor2_email') && $request->filled('supervisor2_address') && $request->filled('supervisor2_mobile')) {

                    Supervisor::where('applicant_id', $application->id)->where('supervisor_type', 2)->delete();

                    $supervisor2 = new Supervisor();
                    $supervisor2->applicant_id = $application->id;
                    $supervisor2->supervisor_type = 2;
                    $supervisor2->full_name = $request->input('supervisor2_name');
                    $supervisor2->email = $request->input('supervisor2_email');
                    $supervisor2->address = $request->input('supervisor2_address');
                    $supervisor2->mobile_no = $request->input('supervisor2_mobile');
                    $supervisor2->tel_home = $request->input('supervisor2_tel_home');
                    $supervisor2->tel_office = $request->input('supervisor2_tel_office');
                    $supervisor2->save();
                }
            }

            $application->ref_name1 = $request->input('referee1_name');
            $application->ref_email1 = $request->input('referee1_email');
            $application->ref_mobile1 = $request->input('referee1_phone');

            $application->ref_name2 = $request->input('referee2_name');
            $application->ref_email2 = $request->input('referee2_email');
            $application->ref_mobile2 = $request->input('referee2_phone');

            // Handle file uploads and save to document_summaries table
            $courseId = $application->open_application_id;
            $applicantId = $application->id;

            // Upload profile photo
            if ($request->hasFile('profile_photo')) {
                $this->handleFileUpload(
                    $request->file('profile_photo'),
                    $applicantId,
                    $courseId,
                    1, // Profile Photo
                    73,  // Document category ID
                    'Profile Photo'  // Document type name
                );
            }

            // Upload degree certificate
            if ($request->hasFile('degree_certificate')) {
                $this->handleFileUpload(
                    $request->file('degree_certificate'),
                    $applicantId,
                    $courseId,
                    2, // Degree Certificate
                    74,  // Document category ID
                    'Degree Certificate'  // Document category ID
                );
            }

            // Upload degree transcript
            if ($request->hasFile('degree_transcript')) {
                $this->handleFileUpload(
                    $request->file('degree_transcript'),
                    $applicantId,
                    $courseId,
                    3, // Degree Transcript
                    75,  // Document category ID
                    'Degree Transcript'  // Document type name
                );
            }

            // Upload birth certificate
            if ($request->hasFile('birth_certificate')) {
                $this->handleFileUpload(
                    $request->file('birth_certificate'),
                    $applicantId,
                    $courseId,
                    4, // Birth Certificate
                    76,  // Document category ID
                    'Birth Certificate'  // Document category ID
                );
            }

            // Upload NIC/Passport document
            if ($request->hasFile('nic_passport_doc')) {
                $this->handleFileUpload(
                    $request->file('nic_passport_doc'),
                    $applicantId,
                    $courseId,
                    5, // NIC/Passport
                    77,  // Document category ID
                    'Identity Document'  // Document type name
                );
            }

            // Upload research proposals (only for research courses)
            if ($request->input('course_main_category') == 42) {
                if ($request->hasFile('research_proposal1')) {
                    $this->handleFileUpload(
                        $request->file('research_proposal1'),
                        $applicantId,
                        $courseId,
                        6, // Research Proposal 1
                        78,  // Document category ID
                        'Research Proposal 1'  // Document type name
                    );
                }

                if ($request->hasFile('research_proposal2')) {
                    $this->handleFileUpload(
                        $request->file('research_proposal2'),
                        $applicantId,
                        $courseId,
                        7, // Research Proposal 2
                        79,  // Document category ID
                        'Research Proposal 2'  // Document type name
                    );
                }
            }

            $application->submit_status = 2;
            $application->basic_data_submit_date = date('Y-m-d');
            $application->save();

            // Call API to check existing student and generate student ID after successful submission
            try {

                $courseData = OpenApplication::join('courses', 'open_applications.course_code', '=', 'courses.id')
                    ->select('open_applications.*', 'courses.course_main_category', 'courses.payment_course_code')
                    ->where('open_applications.id', $application->open_application_id)->firstOrFail();

                // Get student NIC for checking
                $studentNIC = $application->active_nic == 1
                    ? $application->old_nic
                    : ($application->active_nic == 2
                        ? $application->new_nic
                        : ($application->passport ?? null));

                // Create new student directly
                $response = Http::asForm()->post('http://************/API/APIFGSAddStudent.php', [
                    'key' => '977f2bf83fd0e527b77d9155390ff66d',
                    'regYear' => $courseData->reg_year,
                    'courseCode' => $courseData->payment_course_code,
                    'intake' => $courseData->batch,
                    'title' => $application->getTitleName->category_name ?? 'N/A',
                    'initials' => $application->initials ?? 'N/A',
                    'lastName' => $application->last_name ?? 'N/A',
                    'NIC' => $studentNIC,
                    'email' => $application->email ?? 'N/A',
                ]);

                $existingStudentId = null;

                if ($response->successful()) {
                    $data = $response->json(); // decode JSON into array
                    $nStudentId = $data[0]['nStudentId'] ?? null;

                    if ($nStudentId) {
                        $existingStudentId = $nStudentId;

                        Log::info('Student ID generated successfully', [
                            'applicant_id' => $application->id,
                            'student_id' => $nStudentId
                        ]);
                    } else {
                        Log::warning('API response did not contain student ID', [
                            'applicant_id' => $application->id,
                            'response' => $data
                        ]);
                    }
                } else {
                    Log::error('Student creation API call failed', [
                        'applicant_id' => $application->id,
                        'status' => $response->status(),
                        'response' => $response->body()
                    ]);
                }

                // Update application with student ID and payment system status
                if ($existingStudentId) {
                    $application->student_payment_id = $existingStudentId;
                    $application->insert_payment_system_status = 1;
                    $application->insert_payment_system_date = date('Y-m-d');
                    $application->save();

                    Log::info('Application updated with student ID', [
                        'applicant_id' => $application->id,
                        'student_payment_id' => $existingStudentId
                    ]);
                } else {
                    $application->insert_payment_system_status = 2;
                    $application->insert_payment_system_date = date('Y-m-d');
                    $application->save();

                    Log::error('Failed to obtain student ID', [
                        'applicant_id' => $application->id
                    ]);
                }
            } catch (\Exception $e) {
                Log::error('Exception during API call', [
                    'applicant_id' => $application->id,
                    'error' => $e->getMessage()
                ]);
            }

            // Redirect to payment page after successful submission
            $notification = array(
                'message' => 'Application submitted successfully! Please proceed with payment.',
                'alert-type' => 'success'
            );

            return redirect()->route('application.payment', [
                'openAppID' => encrypt($application->open_application_id),
                'id' => encrypt($application->id)
            ])->with($notification);
        }
    }



    public function deleteQualification($id)
    {

        $applicant = Qualification::findOrFail($id);

        $applicant->delete();

        return redirect()->back()->with('error', 'Qualification deleted successfully!');
    }

    public function deleteEmployment($id)
    {
        $employmentRecord = EmployeeRecords::findOrFail($id);
        $employmentRecord->delete();

        return redirect()->back()->with('success', 'Employment record deleted successfully!');
    }


    public function viewDocument($token)
    {
        try {
            // Check if user has verified OTP (basic security check)
            if (!session('otp_verified')) {
                abort(403, 'Access denied');
            }

            // Decrypt the token to get document ID
            $documentId = decrypt($token);

            // Find the document
            $document = DocumentSummary::findOrFail($documentId);

            $sessionApplicantId = session('current_applicant_id');
            if ($sessionApplicantId && $document->applicant_id != $sessionApplicantId) {
                abort(403, 'Access denied');
            }

            if (!Storage::disk('public')->exists($document->file_path)) {
                abort(404, 'File not found');
            }

            // Get file content
            $fileContent = Storage::disk('public')->get($document->file_path);

            // Get file extension to determine content type
            $extension = pathinfo($document->original_file_name, PATHINFO_EXTENSION);

            // Set appropriate content type
            $contentType = match(strtolower($extension)) {
                'pdf' => 'application/pdf',
                'jpg', 'jpeg' => 'image/jpeg',
                'png' => 'image/png',
                'gif' => 'image/gif',
                default => 'application/octet-stream'
            };

            // Return file response
            return response($fileContent)
                ->header('Content-Type', $contentType)
                ->header('Content-Disposition', 'inline; filename="' . $document->original_file_name . '"')
                ->header('Cache-Control', 'no-cache, no-store, must-revalidate')
                ->header('Pragma', 'no-cache')
                ->header('Expires', '0');

        } catch (\Exception $e) {
            abort(404, 'Invalid document token');
        }
    }

    public function ApplicationPayment($openAppID, $id)
    {
        if (!session('otp_verified')) {
            $notification = array(
                'message' => 'Please complete the verification process first.',
                'alert-type' => 'error'
            );

            return redirect()->route('home')->with($notification);
        }

        $CourseOpenId = decrypt($openAppID);
        $applicationId = decrypt($id);

        // Get course and application data
        $courseData = OpenApplication::join('courses', 'open_applications.course_code', '=', 'courses.id')
            ->select('open_applications.*', 'courses.course_main_category','courses.payment_course_code')
            ->where('open_applications.id', $CourseOpenId)->firstOrFail();

        $application = Applicant::find($applicationId);

        // Check if application exists and is submitted (submit_status = 2)
        if (!$application || $application->submit_status != 2) {
            $notification = array(
                'message' => 'Application not found or not submitted yet. Please submit your application first.',
                'alert-type' => 'error'
            );

            return redirect()->route('enroll.list')->with($notification);
        }

        // Get course fees

        if($courseData->course_main_category == 42){

            $courseFees = CourseResearchFee::where('course_code', $courseData->course_code)->where('reg_year', $courseData->reg_year)
            ->where('batch', $courseData->batch)
            ->where('active_status', 1)
            ->where('pay_income_type_id','=', 1)
            ->orderBy('pay_income_type_id')
            ->first();

        }else {

            $courseFees = CourseFees::where('course_code', $courseData->course_code)
            ->where('reg_year', $courseData->reg_year)
            ->where('batch', $courseData->batch)
            ->where('active_status', 1)
            ->where('pay_income_type_id','=', 1)
            ->orderBy('pay_income_type_id')
            ->first();

        }


        return view('frontend.payment.index', compact('courseData', 'CourseOpenId', 'applicationId', 'application', 'courseFees'));
    }

    public function downloadApplicationPDF($openAppID, $id)
    {
        if (!session('otp_verified')) {
            $notification = array(
                'message' => 'Please complete the verification process first.',
                'alert-type' => 'error'
            );

            return redirect()->route('home')->with($notification);
        }

        $CourseOpenId = decrypt($openAppID);
        $applicationId = decrypt($id);

        $courseData = OpenApplication::join('courses', 'open_applications.course_code', '=', 'courses.id')
            ->select('open_applications.*', 'courses.course_main_category')
            ->where('open_applications.id', $CourseOpenId)->firstOrFail();

        $application = Applicant::with(['qualifications.qualificationType', 'documents'])->find($applicationId);

        if (!$application) {
            abort(404, 'Application not found');
        }

        // Check if application exists and is submitted (submit_status = 2)
        if (!$application || $application->submit_status == 1) {
            $notification = array(
                'message' => 'Application not found or not submitted yet.',
                'alert-type' => 'error'
            );

            return redirect()->route('enroll.list')->with($notification);
        }

        // Get employment records
        $employmentRecords = EmployeeRecords::where('applicant_id', $applicationId)->get();

        // Get supervisors for research courses
        $supervisors = [];
        if ($courseData->course_main_category == 42) {
            $supervisors = Supervisor::where('applicant_id', $applicationId)->get();
        }

        // Get categories for display
        $titleName = $application->title_id ? (Category::find($application->title_id)->category_name ?? 'N/A') : 'N/A';
        $genderName = $application->gender_cat_id ? (Category::find($application->gender_cat_id)->category_name ?? 'N/A') : 'N/A';
        $civilStatusName = $application->civil_status_cat_id ? (Category::find($application->civil_status_cat_id)->category_name ?? 'N/A') : 'N/A';
        $className = $application->class_cat_id ? (Category::find($application->class_cat_id)->category_name ?? 'N/A') : 'N/A';

        try {
            // Generate PDF
            $pdf = Pdf::loadView('frontend.pdf.application_summary', compact(
                'courseData',
                'application',
                'employmentRecords',
                'supervisors',
                'titleName',
                'genderName',
                'civilStatusName',
                'className'
            ));

            $pdf->setPaper('A4', 'portrait');
            $pdf->setOptions([
                'isPhpEnabled' => true,
                'isRemoteEnabled' => true,
                'defaultFont' => 'Arial'
            ]);

            $filename = 'Application.pdf';

            return $pdf->stream($filename);

        } catch (\Exception $e) {
            Log::error('PDF generation failed', [
                'applicant_id' => $application->id,
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ]);

            $notification = array(
                'message' => 'Failed to generate PDF: ' . $e->getMessage(),
                'alert-type' => 'error'
            );

            return redirect()->back()->with($notification);
        }
    }

    public function BankPaymentDownload(Request $request)
    {

        try {
            $response = Http::asForm()->post('http://************/API/userFGSPay.php', [
                'courseCode'      => $request->payment_course_code,
                'IncomeTypeCode'  => '01',
                'nic'             => $request->nic,
                'amu'             => $request->amu,
                'opt'             => '1',
                'email'           => $request->email,
                'mobile'          => $request->mobile,
            ]);

            if (!$response->successful()) {
                return response()->json([
                    'error' => 'Payment API failed',
                    'status' => $response->status()
                ], 500);
            }

            $body = $response->body();

            return response($body, 200)
            ->header('Content-Type', 'application/pdf')
            ->header('Content-Disposition', 'inline; filename="bank_payment_slip.pdf"')
            ->header('Content-Length', strlen($body))
            ->header('Accept-Ranges', 'bytes');

        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Payment processing failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function CardPaymentProcess(Request $request)
    {

        try {
            $response = Http::asForm()->post('http://************/API/userFGSPay.php', [
                'courseCode'      => $request->payment_course_code,
                'IncomeTypeCode'  => '01',
                'nic'             => $request->nic,
                'amu'             => $request->amu,
                'opt'             => '2',
                'email'           => $request->email,
                'mobile'          => $request->mobile,
            ]);

            Log::info('Card Payment API Response', [
                'status' => $response->status(),
                'headers' => $response->headers(),
                'content_type' => $response->header('Content-Type'),
                'body_length' => strlen($response->body()),
                'body_preview' => substr($response->body(), 0, 200)
            ]);

            if (!$response->successful()) {
                Log::error('Card Payment API Failed', [
                    'status' => $response->status(),
                    'body' => $response->body()
                ]);

                return response()->json([
                    'error' => 'Payment API failed',
                    'status' => $response->status()
                ], 500);
            }

            $contentType = $response->header('Content-Type') ?: 'text/html';

            return response($response->body(), 200)
                ->header('Content-Type', $contentType);

        } catch (\Exception $e) {
            Log::error('Card Payment Process Exception', [
                'error' => $e->getMessage(),
                'line' => $e->getLine(),
                'file' => $e->getFile()
            ]);

            return response()->json([
                'error' => 'Payment processing failed',
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function applicationFinal($openAppID, $id)
    {
        if (!session('otp_verified')) {
            $notification = array(
                'message' => 'Please complete the verification process first.',
                'alert-type' => 'error'
            );

            return redirect()->route('home')->with($notification);
        }

        $CourseOpenId = decrypt($openAppID);
        $applicationId = decrypt($id);

        // Get course and application data
        $courseData = OpenApplication::join('courses', 'open_applications.course_code', '=', 'courses.id')
            ->select('open_applications.*', 'courses.course_main_category', 'courses.payment_course_code')
            ->where('open_applications.id', $CourseOpenId)->firstOrFail();

        $application = Applicant::with(['getTitleName'])->find($applicationId);

        if (!$application) {
            $notification = array(
                'message' => 'Application not found.',
                'alert-type' => 'error'
            );

            return redirect()->route('home')->with($notification);
        }

        // Check if application has been submitted (status 2 or 3)
        if ($application->submit_status < 2) {
            $notification = array(
                'message' => 'Application has not been submitted yet.',
                'alert-type' => 'error'
            );

            return redirect()->route('application.show', [
                'openAppID' => encrypt($CourseOpenId),
                'id' => encrypt($applicationId)
            ])->with($notification);
        }

        return view('frontend.application.final', compact('courseData', 'application', 'CourseOpenId', 'applicationId'));
    }

}
