<?php

namespace App\Http\Requests;

use App\Models\Course;
use App\Models\StudyBoardSubject;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ResearchApplicationOpenStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'course_code' => [
                'required',
                Rule::unique('open_applications')->where(function ($query) {
                    return $query->where('reg_year', request('reg_year'));
                                 //->where('intake', request('intake'));
                }),
            ],
            'reg_year' => 'required',
            'intake'   => 'required',
        ];
    }

    public function messages()
    {
        return [
            'course_code.required'  => 'Select relavent course.',
            'reg_year.required'     => 'The academic year is required.',
            'intake.required'       => 'The intake field is required.',
            'course_code.unique'    => 'The selected course is already open for this academic year and intake.',
        ];
    }

    public function prepareData()
    {
        $course = Course::find($this->course_code);
        $studyBoardSubjectPhD = StudyBoardSubject::where('study_board_id', $course->study_board_id)
            ->where('phd_eligiblity', 1)
            ->get();
        $studyBoardSubjectMpil = StudyBoardSubject::where('study_board_id', $course->study_board_id)
            ->where('mphil_eligiblity', 1)
            ->get();
        $studyBoardSubjectMar = StudyBoardSubject::where('study_board_id', $course->study_board_id)
            ->where('mar_eligiblity', 1)
            ->get();
        $studyBoardSubjectMat = StudyBoardSubject::where('study_board_id', $course->study_board_id)
            ->where('mat_eligiblity', 1)
            ->get();
        $studyBoardSubjectMaq = StudyBoardSubject::where('study_board_id', $course->study_board_id)
            ->where('maq_eligiblity', 1)
            ->get();

        // Research course
        $data = [];

        if ($course->course_cat_id == 20) {
            // For PhD eligibility
            foreach ($studyBoardSubjectPhD as $subject) {
                $data[] = [
                    'course_code'  => $this->course_code,
                    'subject_code' => $subject->id,
                    'reg_year'     => $this->reg_year,
                    'batch'        => 1,
                    'intake'       => $this->intake,
                    'start_date'   => Carbon::createFromFormat('Y', $this->reg_year)->startOfYear()->format('Y-m-d'),
                    'closing_date' => Carbon::createFromFormat('Y', $this->reg_year)->endOfYear()->format('Y-m-d'),
                    'display_name' => 'PhD in ' .$subject->name,
                    'active_status' => 1,
                    'created_emp'  => $this->user()->reg_no,
                    'created_date' => Carbon::now(),
                ];
            }
        } else if ($course->course_cat_id == 21) {
            // For MPhil eligibility
            foreach ($studyBoardSubjectMpil as $subject) {
                $data[] = [
                    'course_code'  => $this->course_code,
                    'subject_code' => $subject->id,
                    'reg_year'     => $this->reg_year,
                    'batch'        => 1,
                    'intake'       => $this->intake,
                    'start_date'   => Carbon::createFromFormat('Y', $this->reg_year)->startOfYear()->format('Y-m-d'),
                    'closing_date' => Carbon::createFromFormat('Y', $this->reg_year)->endOfYear()->format('Y-m-d'),
                    'display_name' => 'MPhil in ' .$subject->name,
                    'active_status' => 1,
                    'created_emp'  => $this->user()->reg_no,
                    'created_date' => Carbon::now(),
                ];
            }
        } else if ($course->course_cat_id == 22) {
            // For Mar eligibility
            foreach ($studyBoardSubjectMar as $subject) {
                $data[] = [
                    'course_code'  => $this->course_code,
                    'subject_code' => $subject->id,
                    'reg_year'     => $this->reg_year,
                    'batch'        => 1,
                    'intake'       => $this->intake,
                    'start_date'   => Carbon::createFromFormat('Y', $this->reg_year)->startOfYear()->format('Y-m-d'),
                    'closing_date' => Carbon::createFromFormat('Y', $this->reg_year)->endOfYear()->format('Y-m-d'),
                    'display_name' => 'MAR in ' .$subject->name,
                    'active_status' => 1,
                    'created_emp'  => $this->user()->reg_no,
                    'created_date' => Carbon::now(),
                ];
            }
        } else if ($course->course_cat_id == 23) {
            // For Mat eligibility
            foreach ($studyBoardSubjectMat as $subject) {
                $data[] = [
                    'course_code'  => $this->course_code,
                    'subject_code' => $subject->id,
                    'reg_year'     => $this->reg_year,
                    'batch'        => 1,
                    'intake'       => $this->intake,
                    'start_date'   => Carbon::createFromFormat('Y', $this->reg_year)->startOfYear()->format('Y-m-d'),
                    'closing_date' => Carbon::createFromFormat('Y', $this->reg_year)->endOfYear()->format('Y-m-d'),
                    'display_name' => 'MAT in ' .$subject->name,
                    'active_status' => 1,
                    'created_emp'  => $this->user()->reg_no,
                    'created_date' => Carbon::now(),
                ];
            }
        } else if ($course->course_cat_id == 24) {
            // For Maq eligibility
            foreach ($studyBoardSubjectMaq as $subject) {
                $data[] = [
                    'course_code'  => $this->course_code,
                    'subject_code' => $subject->id,
                    'reg_year'     => $this->reg_year,
                    'batch'        => 1,
                    'intake'       => $this->intake,
                    'start_date'   => Carbon::createFromFormat('Y', $this->reg_year)->startOfYear()->format('Y-m-d'),
                    'closing_date' => Carbon::createFromFormat('Y', $this->reg_year)->endOfYear()->format('Y-m-d'),
                    'display_name' => 'MAQ in ' .$subject->name,
                    'active_status' => 1,
                    'created_emp'  => $this->user()->reg_no,
                    'created_date' => Carbon::now(),
                ];
            }
        }

        return $data;
    }
}
