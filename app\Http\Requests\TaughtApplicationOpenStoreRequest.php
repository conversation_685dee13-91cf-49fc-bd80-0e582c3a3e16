<?php

namespace App\Http\Requests;

use App\Models\Course;
use App\Models\StudyBoardSubject;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class TaughtApplicationOpenStoreRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'course_code' => [
                'required',
                Rule::unique('open_applications')->where(function ($query) {
                    return $query->where('reg_year', request('reg_year'))
                                 ->where('batch', request('batch'));

                }),
            ],
            'reg_year'     => 'required',
            'batch'        => 'required',
            'intake'       => 'required',
            'start_date'   => 'required|date',
            'closing_date' => 'required|date',
        ];
    }

    public function messages()
    {
        return [
            'course_code.required'  => 'Select relavent course.',
            'reg_year.required'     => 'The academic year is required.',
            'batch.required'        => 'The batch field is required.',
            'intake.required'       => 'The intake field is required.',
            'start_date.required'   => 'The start date is required.',
            'start_date.date'       => 'The start date must be a valid date.',
            'closing_date.required' => 'The closing date is required.',
            'closing_date.date'     => 'The closing date must be a valid date.',
            'course_code.unique'    => 'The selected course is already open for this academic year and intake.',
        ];
    }

    public function prepareData()
    {
        $course = Course::find($this->course_code);
        $studyBoardSubjectPhD = StudyBoardSubject::where('study_board_id', $course->study_board_id)
            ->where('phd_eligiblity', 1)
            ->get();
        $studyBoardSubjectMpil = StudyBoardSubject::where('study_board_id', $course->study_board_id)
            ->where('mphil_eligiblity', 1)
            ->get();

        if ($course->course_main_category == 41) {
            // Taught course
            return [
                [
                    'course_code'  => $this->course_code,
                    'subject_code' => 0,
                    'reg_year'     => $this->reg_year,
                    'batch'        => $this->batch,
                    'intake'       => $this->intake,
                    'start_date'   => $this->start_date,
                    'closing_date' => $this->closing_date,
                    'display_name' => $course->course_name,
                    'application_status' => 85,
                    'created_emp'  => $this->user()->reg_no,
                    'created_date' => Carbon::now(),
                ]
            ];
        } else if ($course->course_main_category == 42) {
            // Research course
            $data = [];

            if ($course->course_cat_id == 20) {
                // For PhD eligibility
                foreach ($studyBoardSubjectPhD as $subject) {
                    $data[] = [
                        'course_code'  => $this->course_code,
                        'subject_code' => $subject->id,
                        'reg_year'     => $this->reg_year,
                        'batch'        => $this->batch,
                        'intake'       => $this->intake,
                        'start_date'   => $this->start_date,
                        'closing_date' => $this->closing_date,
                        'display_name' => 'PhD in ' .$subject->name,
                        'application_status' => 85,
                        'created_emp'  => $this->user()->reg_no,
                        'created_date' => Carbon::now(),
                    ];
                }
            } else if ($course->course_cat_id == 21) {
                // For MPhil eligibility
                foreach ($studyBoardSubjectMpil as $subject) {
                    $data[] = [
                        'course_code'  => $this->course_code,
                        'subject_code' => $subject->id,
                        'reg_year'     => $this->reg_year,
                        'batch'        => $this->batch,
                        'intake'       => $this->intake,
                        'start_date'   => $this->start_date,
                        'closing_date' => $this->closing_date,
                        'display_name' => 'MPhil in ' .$subject->name,
                        'application_status' => 85,
                        'created_emp'  => $this->user()->reg_no,
                        'created_date' => Carbon::now(),
                    ];
                }
            }

            return $data;
        }
    }
}
