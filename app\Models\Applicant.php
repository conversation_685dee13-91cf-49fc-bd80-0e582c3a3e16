<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Applicant extends Model
{
    use HasFactory;

    protected $guarded = [];

    public function qualifications()
    {
        return $this->hasMany(Qualification::class);
    }

    public function documents()
    {
        return $this->hasMany(DocumentSummary::class);
    }

    public function getTitleName()
    {
        return $this->belongsTo(Category::class, 'title_id', 'id');
    }

    public function getGenderName()
    {
        return $this->belongsTo(Category::class, 'gender_cat_id', 'id');
    }

    public function getCivilStatusName()
    {
        return $this->belongsTo(Category::class, 'civil_status_cat_id', 'id');
    }

    public function getClassificationName()
    {
        return $this->belongsTo(Category::class, 'class_cat_id', 'id');
    }

    public function getDurationName()
    {
        return $this->belongsTo(Category::class, 'duration', 'id');
    }

    public function getApplicationStatus()
    {
        return $this->belongsTo(Category::class, 'application_status', 'id');
    }
}
