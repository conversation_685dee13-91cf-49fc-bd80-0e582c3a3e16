<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('open_applications', function (Blueprint $table) {
            $table->integer('application_status')->default(0)->after('updated_date');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('open_applications', function (Blueprint $table) {
            $table->dropColumn('application_status');
        });
    }
};
