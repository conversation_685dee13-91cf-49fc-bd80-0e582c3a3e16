<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class Category5Seeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $datas =
            [
                ['category_type_id' => 18, 'category_name' => 'Applicant Apply Process', 'display_name' => '','category_code'=> 1 ,'sorting_order' => 1],
                ['category_type_id' => 18, 'category_name' => 'Applicant Sortlist Process', 'display_name' => '','category_code'=> 2 ,'sorting_order' => 2],
                ['category_type_id' => 18, 'category_name' => 'Applicant Registration Process', 'display_name' => '','category_code'=> 3 ,'sorting_order' => 3],

                ['category_type_id' => 19, 'category_name' => 'Applicantion Open', 'display_name' => '','category_code'=> 1 ,'sorting_order' => 1],
                ['category_type_id' => 19, 'category_name' => 'Applicantion Closed', 'display_name' => '','category_code'=> 2 ,'sorting_order' => 2],

            ];

            foreach($datas as $data){
                DB::table('categories')->insert($data);
            }
    }
}
