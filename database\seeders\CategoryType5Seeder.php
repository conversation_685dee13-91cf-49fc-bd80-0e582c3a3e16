<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class CategoryType5Seeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $datas =
            [
                ['name' => 'Applicant Status Type', 'slug' => 'applicant-status-type'],
                ['name' => 'Application Status Type', 'slug' => 'application-status-type'],
            ];

        foreach($datas as $data){
                DB::table('category_types')->insert($data);
        }
    }
}
