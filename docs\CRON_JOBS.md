# Cron Jobs Documentation

## Application Closing Automation

### Overview
The system includes an automated cron job that closes expired applications by setting `active_status = 0` and `application_status = 86` for applications that have passed their closing date.

### Commands Available

#### 1. Close Expired Applications (Production)
```bash
php artisan applications:close-expired
```
- **Purpose**: Automatically closes applications that have passed their closing date
- **Schedule**: Runs daily at 12:01 AM
- **Logic**: 
  - Finds applications with `active_status = 1` and `application_status = 85`
  - Checks if current date > closing_date OR current date > deadline_extented_date (if extension enabled)
  - Updates `active_status = 0` and `application_status = 86`
  - Logs all actions for audit trail

#### 2. Test Close Expired Applications (Testing)
```bash
# Dry run (shows what would be closed without making changes)
php artisan applications:test-close-expired --dry-run

# Live test (actually closes applications with confirmation)
php artisan applications:test-close-expired
```
- **Purpose**: Test the closing functionality before running in production
- **Features**: 
  - Shows table of applications that would be closed
  - Dry run option for safe testing
  - Interactive confirmation for live mode

### Scheduling Configuration

The cron job is configured in `app/Console/Kernel.php`:

```php
protected function schedule(Schedule $schedule): void
{
    // Close expired applications daily at 12:01 AM
    $schedule->command('applications:close-expired')
             ->daily()
             ->at('00:01')
             ->withoutOverlapping()
             ->runInBackground();
}
```

### Server Cron Setup

To enable the Laravel scheduler on your server, add this to your crontab:

```bash
# Edit crontab
crontab -e

# Add this line (replace /path/to/your/project with actual path)
* * * * * cd /path/to/your/project && php artisan schedule:run >> /dev/null 2>&1
```

### Application Status Codes

- **85**: Active application (open for submissions)
- **86**: Closed application (past closing date)

### Active Status Codes

- **1**: Active (application is open)
- **0**: Inactive (application is closed)

### Closing Date Logic

The system considers two possible closing dates:

1. **Regular Closing Date**: `closing_date` field
2. **Extended Closing Date**: `deadline_extented_date` field (only if `deadline_extented_status = 1`)

The effective closing date is determined as follows:
- If `deadline_extented_status = 1` AND `deadline_extented_date` is not null → Use `deadline_extented_date`
- Otherwise → Use `closing_date`

### Logging

All cron job activities are logged to the Laravel log files with the following information:
- Application ID and details
- Course information
- Closing dates (original and extended)
- Success/failure status
- Error messages (if any)

### Manual Execution

You can manually run the cron job at any time:

```bash
# Run the actual closing command
php artisan applications:close-expired

# Test what would be closed (dry run)
php artisan applications:test-close-expired --dry-run

# Test with actual closing (with confirmation)
php artisan applications:test-close-expired
```

### Monitoring

To monitor the cron job execution:

1. **Check Laravel Logs**: Look in `storage/logs/laravel.log` for entries with "CloseExpiredApplications"
2. **Database**: Query applications with `application_status = 86` to see closed applications
3. **Command Output**: Run the test command to see current status

### Troubleshooting

#### Common Issues:

1. **Cron not running**: 
   - Verify server crontab is set up correctly
   - Check if `php artisan schedule:run` works manually

2. **Applications not closing**:
   - Run test command to see what applications are found
   - Check if applications have correct status codes (85 for active)
   - Verify closing dates are in the past

3. **Permission errors**:
   - Ensure web server has write permissions to log files
   - Check database connection and permissions

#### Debug Commands:

```bash
# Check if scheduler is working
php artisan schedule:list

# Run scheduler manually
php artisan schedule:run

# Test the closing logic
php artisan applications:test-close-expired --dry-run

# Check Laravel logs
tail -f storage/logs/laravel.log | grep CloseExpiredApplications
```

### Security Considerations

- The cron job runs with system privileges (`updated_emp = 0`)
- All actions are logged for audit trail
- Uses Laravel's built-in database transactions for data integrity
- Includes error handling to prevent partial updates

### Performance

- Uses efficient database queries with proper indexing
- Processes applications in batches to avoid memory issues
- Includes `withoutOverlapping()` to prevent concurrent executions
- Runs in background to avoid blocking other processes
