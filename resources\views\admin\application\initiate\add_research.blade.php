@extends('admin.admin_master')
@section('admin')
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Research Course Application Initiate</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('application.initiate.index') }}">Applications</a></li>
                        <li class="breadcrumb-item active">Research Initiate</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    @auth
                        <div class="card card-{{ auth()->user()->sidebar_color }}">
                        @endauth
                        <div class="card-header">
                            <h3 class="card-title">
                                <a href="{{ URL::previous() }}">
                                    <i class="fa fa-arrow-circle-left" aria-hidden="true" style="font-size: 30px;"></i>
                                </a>
                                Available Research Courses for Initiation
                            </h3>
                            <div class="card-tools">
                                <span class="badge badge-info">Academic Year: {{ $currentYear }}</span>
                                <span class="badge badge-success">Intake: 1</span>
                            </div>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                            @if($courses->count() > 0)
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="10%">Course Code</th>
                                                <th width="30%">Course Name</th>
                                                <th width="20%">Study Board</th>
                                                <th width="15%">Category</th>
                                                <th width="10%">Status</th>
                                                <th width="15%">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($courses as $course)
                                            <tr>
                                                <td>{{ $course->id }}</td>
                                                <td>{{ $course->course_name }}</td>
                                                <td>{{ $course->studyBoardName->name ?? 'N/A' }}</td>
                                                <td>{{ $course->courseCategory->category_name ?? 'N/A' }}</td>
                                                <td>
                                                    @if($course->active_status == 1)
                                                        <span class="badge badge-success">Active</span>
                                                    @else
                                                        <span class="badge badge-danger">Inactive</span>
                                                    @endif
                                                    @if($course->is_initiated)
                                                        <br><span class="badge badge-info mt-1">Initiated {{ $currentYear }}</span>
                                                    @endif
                                                </td>
                                                <td>
                                                    @can('application.initiate.research.create')
                                                        @if($course->is_initiated)
                                                            <button type="button" class="btn btn-sm btn-secondary" disabled>
                                                                <i class="fas fa-check mr-1"></i>Already Initiated
                                                            </button>
                                                        @else
                                                            <form method="POST" action="{{ route('application.initiate.research.quick', $course->id) }}" style="display: inline-block;">
                                                                @csrf
                                                                <button type="submit" class="btn btn-sm btn-success"
                                                                        onclick="return confirm('Are you sure you want to initiate application for {{ $course->course_name }} for academic year {{ $currentYear }}?')">
                                                                    <i class="fas fa-play mr-1"></i>Initiate
                                                                </button>
                                                            </form>
                                                        @endif
                                                    @else
                                                        <span class="text-muted">No Permission</span>
                                                    @endcan
                                                </td>
                                            </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            @else
                                <div class="alert alert-info">
                                    <h5><i class="icon fas fa-info"></i> No Courses Available!</h5>
                                    No research courses are currently available for application initiation.
                                </div>
                            @endif
                        </div>
                        <!-- /.card-body -->
                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-8">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        Clicking "Initiate" will create application records for academic year {{ $currentYear }} with intake 1.
                                        <br>
                                        <i class="fas fa-check-circle text-info"></i>
                                        Courses already initiated for {{ $currentYear }} will show a disabled button.
                                    </small>
                                </div>
                                <div class="col-md-4 text-right">
                                    <a href="{{ route('application.initiate.index') }}" class="btn btn-secondary">
                                        <i class="fas fa-list mr-1"></i>View All Applications
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
@endsection
