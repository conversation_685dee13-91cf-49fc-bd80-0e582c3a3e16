@extends('admin.admin_master')
@section('admin')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Course Application Open List</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Home</a></li>
                    <li class="breadcrumb-item active">Course Application Open List</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <div class="box-header">
                        <h4 class="box-title"><b>Taught Course Application</b></h4>
                    </div>
                    <!-- /.box-header -->
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example1" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Course Code</th>
                                        <th>Name</th>
                                        <th>Study Board</th>
                                        <th>Course Category</th>
                                        <th data-priority="2">Course Application</th>
                                        <th width="15%" data-priority="1">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($taughtCourseData as $course)
                                    <tr>
                                        <td>{{ $course->course_code }}</td>
                                        <td>{{ $course->course_name }}</td>
                                        <td>{{ $course->course->studyBoardName->name ?? 'N/A' }}</td>
                                        <td>{{ $course->course->courseCategory->category_name ?? 'N/A' }}</td>
                                        <td>
                                            <div class="mb-2">
                                                <strong>Year:</strong> {{ $course->reg_year }} ,
                                                <strong>Batch:</strong> {{ $course->batch }}<br>
                                                <strong>Start:</strong> {{ $course->start_date }}<br>
                                                <strong>End:</strong> {{ $course->closing_date }}<br>
                                                <strong>Status:</strong>
                                                @if($course->active_status)
                                                    <span class="badge badge-success">Active</span>
                                                @else
                                                    <span class="badge badge-danger">Inactive</span>
                                                @endif
                                            </div>
                                        </td>
                                        <td>

                                            @if($course->active_status == 0)
                                                <a href="{{ route('application.initiate.show', $course->id) }}" class="btn btn-success btn-sm">Activate</a>
                                                <a href="{{ route('application.initiate.edit', $course->id) }}" class="btn btn-info btn-sm">Edit</a>
                                            @else
                                                <a href="{{ route('application.initiate.show', $course->id) }}" class="btn btn-dark btn-sm">Deactivate</a>
                                            @endif
                                            <a href="{{ route('application.initiate.delete', $course->id) }}" class="btn btn-danger btn-sm"
                                               onclick="return confirm('Are you sure you want to delete this course?');">
                                               Delete
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>

                            </table>
                        </div>
                    </div>
                    <!-- /.box-body -->
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <br>
        <!-- /.row -->
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <div class="box-header">
                        <h4 class="box-title"><b>Research Course Application</b></h4>
                    </div>
                    <!-- /.box-header -->
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example4" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Course Code</th>
                                        <th>Name</th>
                                        <th>Subject Area</th>
                                        <th>Study Board</th>
                                        <th>Course Category</th>
                                        <th data-priority="2">Course Application</th>
                                        <th width="15%" data-priority="1">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($researchCourseData as $course)
                                    <tr>
                                        <td>{{ $course->course_code }}</td>
                                        <td>{{ $course->course_name }}</td>
                                        <td>{{ $course->subject->name }}</td>
                                        <td>{{ $course->course->studyBoardName->name ?? 'N/A' }}</td>
                                        <td>{{ $course->course->courseCategory->category_name ?? 'N/A' }}</td>
                                        <td>
                                            <div class="mb-2">
                                                <strong>Year:</strong> {{ $course->reg_year }} ,
                                                <strong>Batch:</strong> {{ $course->batch }}<br>
                                                <strong>Start:</strong> {{ $course->start_date }}<br>
                                                <strong>End:</strong> {{ $course->closing_date }}<br>
                                                <strong>Status:</strong>
                                                @if($course->active_status)
                                                    <span class="badge badge-success">Active</span>
                                                @else
                                                    <span class="badge badge-danger">Inactive</span>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            {{-- <a href="{{ route('application.initiate.edit', $course->id) }}" class="btn btn-info btn-sm">Edit</a> --}}
                                            @if($course->active_status == 0)
                                                <a href="{{ route('application.initiate.show', $course->id) }}" class="btn btn-success btn-sm">Activate</a>
                                            @else
                                                <a href="{{ route('application.initiate.show', $course->id) }}" class="btn btn-dark btn-sm">Deactivate</a>
                                            @endif

                                            {{-- <a href="{{ route('application.initiate.show', $course->id) }}" class="btn btn-success btn-sm">Activate</a> --}}
                                            <a href="{{ route('application.initiate.delete', $course->id) }}" class="btn btn-danger btn-sm" id="delete">Delete
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>

                            </table>
                        </div>
                    </div>
                    <!-- /.box-body -->
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection
