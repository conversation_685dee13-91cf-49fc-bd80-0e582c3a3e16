@extends('frontend.frontend_master')
@section('frontend')
<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card" style="background-color:#990000">
                        <div class="card-header">
                            <h3 class="card-title text-white"><a href="{{ route('active.application.list') }}"><i class="fa fa-arrow-circle-left" aria-hidden="true" style="font-size: 23px;"></i></a>&nbsp; &nbsp;Applied Application List</h3>
                        </div>
                    </div>
                    <!-- /.box-header -->
                    <div class="card-body">

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="example2">
                                <thead class="bg-light">
                                    <tr>
                                        <th>SN</th>
                                        <th>Ref. No.</th>
                                        <th>Course Name</th>
                                        <th>Closing Date</th>
                                        <th>Application Status</th>
                                        <th>Applicant Status</th>
                                        <th width="25%">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($applicationList as $data)
                                    <tr>
                                        <td>{{ $loop->iteration }}</td>
                                        <td>{{ $data->reference_no }}</td>
                                        <td>{{ $data->display_name }}</td>
                                        @if($data->course_application_open_method == 44)
                                        <td>{{ \Carbon\Carbon::parse($data->closing_date)->format('Y-F-d') }}</td>
                                        @else
                                        <td> N/A </td>
                                        @endif
                                        <td> <span class="badge badge-pill badge-dark">{{ $data->getApplicationStatus->category_name ?? '' }} </span></td>
                                        <td>
                                            @if ($data->submit_status == 1)
                                            <span class="badge badge-pill badge-danger">Application Draft</span>
                                            @elseif ($data->submit_status == 2)
                                            <span class="badge badge-pill badge-warning">Application Payment Pending</span>
                                            @elseif ($data->submit_status == 3)
                                            <span class="badge badge-pill badge-success">Application Completed with payment</span>
                                            @endif
                                        </td>

                                        <td>
                                             @if ($data->submit_status == 1)
                                             @if($data->application_status_open_app == 1)
                                            <a href="{{ route('application.show', ['openAppID' => encrypt($data->open_application_id), 'id' => encrypt($data->applicant_id)]) }}" class="btn btn-sm btn-danger"><i class="fas fa-mouse-pointer mr-1"></i>Resume</a>
                                            @endif
                                            @elseif ($data->submit_status == 2)
                                            @if($data->application_status_open_app == 1)
                                            <a href="{{ route('application.payment', ['openAppID' => encrypt($data->open_application_id), 'id' => encrypt($data->applicant_id)]) }}" class="btn btn-sm btn-warning"><i class="fas fa-mouse-pointer mr-1"></i>Process Payment</a>
                                            @endif

                                            <a href="{{ route('application.download.pdf', ['openAppID' => encrypt($data->open_application_id), 'id' => encrypt($data->applicant_id)]) }}" class="btn btn-sm btn-info" target="_blank"><i class="fas fa-file-pdf mr-1"></i>Download Application</a>
                                            @elseif ($data->submit_status == 3)
                                            @if($data->application_status_open_app == 1)
                                            <a href="{{ route('application.final', ['openAppID' => encrypt($data->open_application_id), 'id' => encrypt($data->applicant_id)]) }}" class="btn btn-sm btn-success"><i class="fas fa-mouse-pointer mr-1"></i>View Application</a>
                                            @endif

                                            <a href="{{ route('application.download.pdf', ['openAppID' => encrypt($data->open_application_id), 'id' => encrypt($data->applicant_id)]) }}" class="btn btn-sm btn-info" target="_blank"><i class="fas fa-file-pdf mr-1"></i>Download Application</a>
                                            @endif
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
@endsection
