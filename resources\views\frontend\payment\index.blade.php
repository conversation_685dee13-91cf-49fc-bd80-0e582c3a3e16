@extends('frontend.frontend_master')
@section('frontend')
    <meta name="csrf-token" content="{{ csrf_token() }}">
    @if(config('app.debug'))
        <div class="container-fluid">
            <div class="alert alert-info">
                <h6>Debug Information:</h6>
                <ul class="mb-0">
                    <li>Course Data: {{ isset($courseData) ? 'Available' : 'Missing' }}</li>
                    <li>Application: {{ isset($application) ? 'Available' : 'Missing' }}</li>
                    <li>Course Fees: {{ isset($courseFees) ? (is_object($courseFees) ? get_class($courseFees) . ' (' . $courseFees->count() . ' items)' : gettype($courseFees)) : 'Missing' }}</li>
                    <li>Course Open ID: {{ isset($CourseOpenId) ? $CourseOpenId : 'Missing' }}</li>
                    <li>Application ID: {{ isset($applicationId) ? $applicationId : 'Missing' }}</li>
                    <li>Student ID: {{ isset($application->student_payment_id) ? $application->student_payment_id : 'Missing' }}</li>
                </ul>
            </div>
        </div>
    @endif

    <!-- Header Section -->
    <section class="content-header mb-2">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <h3 class="text-center">{{ $courseData->display_name }}</h3>
                </div>
            </div>
        </div>
    </section>

    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="application-wrapper">
                    <div class="card">
                        {{-- <div class="card-header text-white" style="background-color: #990000;">
                            <h4 class="mb-0">
                                <i class="fas fa-credit-card"></i>
                                Payment for {{ $courseData->display_name }}
                            </h4>
                        </div> --}}
                        <div class="card-body">
                            <!-- Application Summary -->
                            <div class="row mb-4">
                                <div class="col-md-8">
                                    <div class="card" style="border-color: #990000;">
                                        <div class="card-header text-white" style="background-color: #990000;">
                                            <h5 class="mb-0"><i class="fas fa-info-circle"></i> Application Summary</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <p><strong>Applicant Name:</strong> {{ $application->getTitleName->category_name ?? '' }} {{ $application->name_denoted_by_initials }} {{ $application->last_name }}</p>
                                                    <p><strong>Reference No:</strong> {{ $application->reference_no }}</p>
                                                    <p><strong>Status:</strong> @if ($application->submit_status == 1)
                                            <span class="badge badge-pill badge-danger">Application Draft</span>
                                            @elseif ($application->submit_status == 2)
                                            <span class="badge badge-pill badge-warning">Application Payment Pending</span>
                                            @elseif ($application->submit_status == 3)
                                            <span class="badge badge-pill badge-success">Application Completed with payment</span>
                                            @endif</p>


                                                </div>
                                                <div class="col-md-6">
                                                    <p><strong>Email:</strong> {{ $application->email }}</p>
                                                    <p><strong>Mobile:</strong> {{ $application->mobile_no }}</p>
                                                    <p><strong>Submission Date:</strong> {{ $application->basic_data_submit_date ? date('d/m/Y', strtotime($application->basic_data_submit_date)) : 'N/A' }}</p>
                                                </div>
                                            </div>



                                            <!-- Download PDF Button -->
                                            <div class="mt-3 text-center">
                                                <hr style="border-color: #990000; margin: 15px 0;">
                                                <p class="mb-2" style="font-size: 12px; color: #666;">
                                                    <i class="fas fa-info-circle"></i> Download your complete application summary for your records
                                                </p>
                                                <a href="{{ route('application.download.pdf', ['openAppID' => encrypt($CourseOpenId), 'id' => encrypt($applicationId)]) }}"
                                                   class="btn btn-outline-danger btn-sm"
                                                   target="_blank"
                                                   title="Download complete application summary as PDF">
                                                    <i class="fas fa-file-pdf"></i> Download Application PDF
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="card" style="border-color: #990000;">
                                        <div class="card-header text-white" style="background-color: #990000;">
                                            <h5 class="mb-0"><i class="fas fa-dollar-sign"></i> Application Payment Details</h5>
                                        </div>
                                        <div class="card-body text-center">
                                            @if(isset($courseFees) && $courseFees->count() > 0)

                                                <h3 class="text-success">LKR {{ number_format($courseFees->amount, 2) }}</h3>
                                                <p class="mb-0">Application Fees</p>
                                            @else
                                                <h3 class="text-warning">Application Fee Not Set</h3>
                                                <p class="mb-0">Please contact FGS</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>


                            <!-- Payment Instructions -->
                            <div class="card mb-4" style="border-color: #990000;">
                                <div class="card-header text-white" style="background-color: #990000;">
                                    <h5 class="mb-0"><i class="fas fa-exclamation-triangle"></i> Important Payment Instructions</h5>
                                </div>
                                <div class="card-body">
                                    <div class="alert alert-dark">
                                        <h6><i class="fas fa-info-circle"></i> Please read carefully before proceeding with payment:</h6>
                                        <ul class="mb-0">
                                            <li>Payment must be completed within <strong>7 days</strong> of application submission</li>
                                            <li>Keep your payment receipt/transaction ID for future reference</li>
                                            <li>Payment confirmation may take 1-2 business days for bank deposits</li>
                                            <li>For any payment issues, contact our support team with your reference number</li>
                                            <li>Refunds are subject to university policy and terms & conditions</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>

                            <!-- Payment Options -->
                            <div class="card" style="border-color: #990000;">
                                <div class="card-header text-white" style="background-color: #990000;">
                                    <h5 class="mb-0"><i class="fas fa-payment"></i> Choose Your Payment Method</h5>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <!-- Bank Deposit Option -->

                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 payment-option" data-payment="bank">
                                                <div class="card-body text-center">
                                                    <div class="payment-icon mb-3">
                                                        <i class="fas fa-university fa-3x" style="color: #990000;"></i>
                                                    </div>
                                                    <h5 class="card-title">Bank Deposit</h5>
                                                    <p class="card-text">Transfer funds directly to our bank account</p>
                                                    <ul class="list-unstyled text-left">
                                                        <li><i class="fas fa-check text-success"></i> Secure bank transfer</li>
                                                        <li><i class="fas fa-check text-success"></i> 1-2 business days processing</li>
                                                        <li><i class="fas fa-check text-success"></i> No additional fees</li>
                                                    </ul>
                                                </div>
                                                @if(isset($courseFees->amount) && $courseFees->amount > 0 && $application->student_payment_id != 0)
                                                <form id="bankPaymentForm" action="{{ route('bank.payment.download') }}" method="POST" target="_blank">
                                                @csrf
                                                <input type="hidden" name="payment_course_code" value="{{ $courseData->payment_course_code }}">
                                                <input type="hidden" name="nic" value="{{ $application->active_nic == 1 ? $application->old_nic : ($application->active_nic == 2 ? $application->new_nic : ($application->passport ?? null)) }}">
                                                <input type="hidden" name="amu" value="{{ $courseFees->amount ?? 0 }}">
                                                <input type="hidden" name="email" value="{{ $application->email }}">
                                                <input type="hidden" name="mobile" value="{{ $application->mobile_no }}">
                                                <div class="card-footer">
                                                     <button class="btn btn-block payment-btn" data-payment="card" style="background-color: #990000; border-color: #990000; color: white;" type="submit">
                                                        <i class="fas fa-university"></i> Pay via Bank Deposit
                                                    </button>
                                                </div>
                                                </form>
                                                @else
                                                <div class="card-footer">
                                                    <button class="btn btn-block payment-btn" data-payment="card" style="background-color: #990000; border-color: #990000; color: white;" type="submit" disabled>
                                                        <i class="fas fa-university"></i> Pay via Bank Deposit
                                                    </button>
                                                </div>
                                                @endif
                                            </div>
                                        </div>


                                        <!-- Credit Card Option -->
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 payment-option" data-payment="card">
                                                <div class="card-body text-center">
                                                    <div class="payment-icon mb-3">
                                                        <i class="fas fa-credit-card fa-3x" style="color: #990000;"></i>
                                                    </div>
                                                    <h5 class="card-title">Credit Card</h5>
                                                    <p class="card-text">Pay instantly with your credit/debit card</p>
                                                    <ul class="list-unstyled text-left">
                                                        <li><i class="fas fa-check text-success"></i> Instant payment</li>
                                                        <li><i class="fas fa-check text-success"></i> Secure SSL encryption</li>
                                                        <li><i class="fas fa-check text-success"></i> Visa, MasterCard accepted</li>
                                                    </ul>
                                                </div>
                                               @if(isset($courseFees->amount) && $courseFees->amount > 0 && $application->student_payment_id != 0)
                                                <form id="cardPaymentForm">
                                                @csrf
                                                <input type="hidden" name="payment_course_code" value="{{ $courseData->payment_course_code }}">
                                                <input type="hidden" name="nic" value="{{ $application->active_nic == 1 ? $application->old_nic : ($application->active_nic == 2 ? $application->new_nic : ($application->passport ?? null)) }}">
                                                <input type="hidden" name="amu" value="{{ $courseFees->amount ?? 0 }}">
                                                <input type="hidden" name="email" value="{{ $application->email }}">
                                                <input type="hidden" name="mobile" value="{{ $application->mobile_no }}">

                                                <div class="card-footer">
                                                    <button class="btn btn-block payment-btn" data-payment="card" style="background-color: #990000; border-color: #990000; color: white;" type="submit">
                                                        <i class="fas fa-credit-card"></i> Pay with Card
                                                    </button>
                                                </div>
                                            </form>
                                            
                                            </div>
                                        </div>

                                        <!-- QR Payment Option (Disabled) -->
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 payment-option-disabled" style="opacity: 0.6; cursor: not-allowed;">
                                                <div class="card-body text-center">
                                                    <div class="payment-icon mb-3">
                                                        <i class="fas fa-qrcode fa-3x text-secondary"></i>
                                                    </div>
                                                    <h5 class="card-title text-secondary">QR Payment</h5>
                                                    <p class="card-text text-muted">Scan QR code with your mobile banking app</p>
                                                    <ul class="list-unstyled text-left">
                                                        <li><i class="fas fa-times text-muted"></i> Currently unavailable</li>
                                                        <li><i class="fas fa-times text-muted"></i> Coming soon</li>
                                                        <li><i class="fas fa-times text-muted"></i> Use other payment methods</li>
                                                    </ul>
                                                </div>
                                                <div class="card-footer">
                                                    <button class="btn btn-block btn-secondary" disabled>
                                                        <i class="fas fa-qrcode"></i> QR Payment
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Support Information -->
                            <div class="card mt-4" style="border-color: #990000;">
                                <div class="card-header text-white" style="background-color: #990000;">
                                    <h6 class="mb-0"><i class="fas fa-headset"></i> Need Help?</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Email Support:</strong> <EMAIL></p>
                                            <p><strong>Phone Support:</strong> +94 11 2758759</p>
                                        </div>
                                        <div class="col-md-6">
                                            <p><strong>Office Hours:</strong> Monday - Friday, 9:00 AM - 5:00 PM</p>
                                            <p><strong>Reference Number:</strong> {{ $application->reference_no }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Payment Method Modals will be added here -->

    <style>
        :root {
            --primary-color: #990000;
            --primary-hover: #770000;
            --primary-light: rgba(153, 0, 0, 0.1);
        }

        .payment-option {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }

        .payment-option:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(153, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .payment-icon {
            transition: all 0.3s ease;
        }

        .payment-option:hover .payment-icon {
            transform: scale(1.1);
        }

        .payment-option:hover .payment-icon i {
            color: var(--primary-color) !important;
            text-shadow: 0 2px 4px rgba(153, 0, 0, 0.3);
        }

        .application-wrapper {
            width: 100%;
            max-width: 100%;
            margin: 0;
            box-sizing: border-box;
        }

        .payment-btn:hover {
            background-color: var(--primary-hover) !important;
            border-color: var(--primary-hover) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(153, 0, 0, 0.3);
        }

        .card-header {
            border-bottom: 3px solid rgba(255, 255, 255, 0.2);
        }

        .text-success {
            color: var(--primary-color) !important;
        }

        .alert-info {
            background-color: var(--primary-light);
            border-color: var(--primary-color);
            color: #333;
        }

        .alert-info .fas {
            color: var(--primary-color);
        }

        .list-unstyled .fas.fa-check {
            color: var(--primary-color) !important;
        }

        .payment-option .card-title {
            color: var(--primary-color);
            font-weight: 600;
        }

        .btn-outline-danger:hover {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(153, 0, 0, 0.2);
        }

        .btn-outline-danger {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transition: all 0.3s ease;
        }

        .btn-outline-danger .fas {
            margin-right: 5px;
        }

        /* Disabled payment option styles */
        .payment-option-disabled {
            background-color: #f8f9fa !important;
            border-color: #6c757d !important;
            transition: none !important;
        }

        .payment-option-disabled:hover {
            transform: none !important;
            box-shadow: none !important;
        }

        .payment-option-disabled .card-title {
            color: #6c757d !important;
        }

        .payment-option-disabled .card-text {
            color: #6c757d !important;
        }

        .payment-option-disabled .btn:disabled {
            cursor: not-allowed !important;
            opacity: 0.6 !important;
        }
    </style>

    <script>
document.getElementById('cardPaymentForm').addEventListener('submit', function (e) {
    e.preventDefault();

    const csrfToken = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

    // Show loading indicator
    const submitBtn = this.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
    submitBtn.disabled = true;

    // Create FormData from the form
    const formData = new FormData(this);

    fetch('{{ route('card.payment.process') }}', {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': csrfToken
        },
        body: formData
    })
    .then(response => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return response.text();
    })
    .then(html => {
        console.log('Card payment response:', html.substring(0, 200));

        // Open popup window for payment gateway
        const popup = window.open('', 'PaymentPopup', 'width=800,height=600,scrollbars=yes,resizable=yes');

        if (popup) {
            popup.document.open();
            popup.document.write(html);
            popup.document.close();
        } else {
            alert('Popup blocked! Please allow popups for this site to complete payment.');
        }
    })
    .catch(err => {
        // Reset button
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;

        console.error('Payment error:', err);
        alert('Something went wrong. Please try again. Error: ' + err.message);
    });
});



</script>

@endsection
