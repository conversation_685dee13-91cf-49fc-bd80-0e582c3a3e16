<?php $__env->startSection('frontend'); ?>
<section class="content">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card card-outline card-danger mt-5">
                    <div class="card-body">
                        <h4 class="mb-4 text-center">OTP Verification</h4>
                        <form action="<?php echo e(route('taught.application.verifyotp')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <input type="hidden" name="nic" value="<?php echo e($userDetail['nic']); ?>">
                            <input type="hidden" name="mobile" value="<?php echo e($userDetail['mobile']); ?>">
                            <input type="hidden" name="email" value="<?php echo e($userDetail['email']); ?>">
                            <div class="form-group">
                                <label for="otp">Enter OTP</label>
                                <input type="text" class="form-control" id="otp" name="otp" required>
                            </div>
                            <button type="submit" class="btn btn-danger btn-block">Verify & Continue</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/frontend/taught_application_otp.blade.php ENDPATH**/ ?>