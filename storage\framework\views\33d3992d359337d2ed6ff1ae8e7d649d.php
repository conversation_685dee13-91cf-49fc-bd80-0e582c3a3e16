<?php $__env->startSection('frontend'); ?>
<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card" style="background-color:#990000">
                        <div class="card-header">
                            <h3 class="card-title text-white"><a href="<?php echo e(route('active.application.list')); ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true" style="font-size: 23px;"></i></a>&nbsp; &nbsp;Taught Course Applications</h3>
                        </div>
                    </div>
                    <!-- /.box-header -->
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="table1">
                                <thead class="bg-light">
                                    <tr>
                                        <th>SN</th>
                                        <th>Course Name</th>
                                        <th>Study Board</th>
                                        <th>Category</th>
                                        <th>Application Start Date</th>
                                        <th>Application Closing Date</th>
                                        <th width="10%">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $taughtCourseData; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($loop->iteration); ?></td>
                                        <td><?php echo e($course->course_name); ?></td>
                                        <td><?php echo e($course->course->studyBoardName->name); ?></td>
                                        <td><?php echo e($course->course->courseCategory->category_name); ?></td>
                                        <td><?php echo e(\Carbon\Carbon::parse($course->start_date)->format('Y-F-d')); ?></td>
                                        <td><?php echo e(\Carbon\Carbon::parse($course->closing_date)->format('Y-F-d')); ?></td>
                                        <td class="text-center">
                                            <a href="<?php echo e(route('application.registration.form', encrypt($course->id))); ?>" class="btn btn-sm btn-danger"><i class="fas fa-mouse-pointer mr-1"></i>Apply Now</a>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/frontend/taught_application_list.blade.php ENDPATH**/ ?>