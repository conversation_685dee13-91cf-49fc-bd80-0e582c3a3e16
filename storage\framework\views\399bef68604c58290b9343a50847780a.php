<?php $__env->startSection('admin'); ?>
<!-- Content Header (Page header) -->
    <!-- Main content -->
    <section class="content">
      <div class="container-fluid">
        <div class="row">
          <!-- left column -->
          <div class="col-md-12">
            <!-- general form elements -->
            <?php if(auth()->guard()->check()): ?>
            <div class="card card-<?php echo e(auth()->user()->sidebar_color); ?>">
            <?php endif; ?>
              <div class="card-header">
                <h3 class="card-title"><a href="<?php echo e(URL::previous()); ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true" style="font-size: 30px;"></i></a> Study Board Subject Edit Form</h3>
              </div>
              <!-- /.card-header -->
              <!-- form start -->
              <form method="post" action="<?php echo e(route('study.board.subject.update',$editData->id)); ?>">
                <?php echo csrf_field(); ?>
                <div class="card-body">
                   <div class="row">
                    <div class="col-md-12">
                      <div class="form-group">
                        <label for="">Study Board<span class="text-danger"> *</span></label>
                        <select name="study_board_id" id="study_board_id" class="select2bs4" style="width: 100%">
                            <option value="" disabled>Select Study Board Type</option>
                            <?php $__currentLoopData = $studyBoards; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $studyBoard): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <option value="<?php echo e($studyBoard->id); ?>" <?php echo e($studyBoard->id == $editData->study_board_id ? 'selected' : ''); ?>><?php echo e($studyBoard->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                        </select>
                          <span class="text-danger"><?php $__errorArgs = ['study_board_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                      </div>
                      </div><!-- col-md-6 -->
                   </div><!-- row -->

                   <div class="row">

                    <div class="col-md-12">
                      <div class="form-group">
                        <label for="">Study Board Subject Area Name<span class="text-danger"> *</span></label>
                        <input type="text" class="form-control" id="name" name="name" value="<?php echo e($editData->name); ?>">
                        <span class="text-danger"><?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                      </div>
                      </div><!-- col-md-6 -->
                   </div><!-- row -->

                   <div class="row">
                    <div class="col-md-6">
                      <div class="form-group">
                        <label for="">PhD Application Eligibility<span class="text-danger"> *</span></label>
                        <select name="phd_eligiblity" id="phd_eligiblity" class="select2bs4" style="width: 100%">
                            <option value="" selected disabled>Select Eligibility</option>
                            <option value="1" <?php echo e(1 == $editData->phd_eligiblity ? 'selected' : ''); ?>>Yes</option>
                            <option value="0" <?php echo e(0 == $editData->phd_eligiblity ? 'selected' : ''); ?>>No</option>
                        </select>
                          <span class="text-danger"><?php $__errorArgs = ['phd_eligiblity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                      </div>
                      </div><!-- col-md-6 -->
                      <div class="col-md-6">
                        <div class="form-group">
                          <label for="">MPhil Application Eligibility<span class="text-danger"> *</span></label>
                          <select name="mphil_eligiblity" id="mphil_eligiblity" class="select2bs4" style="width: 100%">
                            <option value="" selected disabled>Select Eligibility</option>
                            <option value="1" <?php echo e(1 == $editData->mphil_eligiblity ? 'selected' : ''); ?>>Yes</option>
                            <option value="0" <?php echo e(0 == $editData->mphil_eligiblity ? 'selected' : ''); ?>>No</option>
                          </select>
                            <span class="text-danger"><?php $__errorArgs = ['mphil_eligiblity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                        </div>
                        </div><!-- col-md-6 -->
                   </div><!-- row -->

                   <div class="row">
                    <div class="col-md-4">
                      <div class="form-group">
                        <label for="">MAR Application Eligibility<span class="text-danger"> *</span></label>
                        <select name="mar_eligiblity" id="mar_eligiblity" class="select2bs4" style="width: 100%">
                            <option value="" selected disabled>Select Eligibility</option>
                            <option value="1" <?php echo e(1 == $editData->mar_eligiblity ? 'selected' : ''); ?>>Yes</option>
                            <option value="0" <?php echo e(0 == $editData->mar_eligiblity ? 'selected' : ''); ?>>No</option>
                        </select>
                          <span class="text-danger"><?php $__errorArgs = ['mar_eligiblity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                      </div>
                      </div><!-- col-md-4 -->
                      <div class="col-md-4">
                        <div class="form-group">
                          <label for="">MAT Application Eligibility<span class="text-danger"> *</span></label>
                          <select name="mat_eligiblity" id="mat_eligiblity" class="select2bs4" style="width: 100%">
                            <option value="" selected disabled>Select Eligibility</option>
                            <option value="1" <?php echo e(1 == $editData->mat_eligiblity ? 'selected' : ''); ?>>Yes</option>
                            <option value="0" <?php echo e(0 == $editData->mat_eligiblity ? 'selected' : ''); ?>>No</option>
                          </select>
                            <span class="text-danger"><?php $__errorArgs = ['mat_eligiblity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                        </div>
                        </div><!-- col-md-4 -->
                        <div class="col-md-4">
                            <div class="form-group">
                              <label for="">MAQ Application Eligibility<span class="text-danger"> *</span></label>
                              <select name="maq_eligiblity" id="maq_eligiblity" class="select2bs4" style="width: 100%">
                                <option value="" selected disabled>Select Eligibility</option>
                                <option value="1" <?php echo e(1 == $editData->maq_eligiblity ? 'selected' : ''); ?>>Yes</option>
                                <option value="0" <?php echo e(0 == $editData->maq_eligiblity ? 'selected' : ''); ?>>No</option>
                              </select>
                                <span class="text-danger"><?php $__errorArgs = ['maq_eligiblity'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                            </div>
                            </div><!-- col-md-6 -->
                   </div><!-- row -->

                </div>
                  <!-- /.card-body -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('study.board.subject.updation')): ?>
                <div class="card-footer">
                    <?php if(auth()->guard()->check()): ?>
                    <input type="submit" class="btn bg-<?php echo e(auth()->user()->sidebar_color); ?>" value="Update">
                    <a href="<?php echo e(route('study.board.subject.index')); ?>" class="btn <?php echo e(auth()->user()->sidebar_color == 'yellow' ? 'btn-dark' : 'btn-warning'); ?>">Cancel</a>
                    <?php endif; ?>
                    </div>
                <?php endif; ?>

                </form>
              </div>
              <!-- /.card -->
            </div>
            <!--/.col (left) -->
          </div>
          <!-- /.row -->
      </div><!-- /.container-fluid -->
  </section>
        <!-- /.content -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/admin/setups/study_board_subject/edit.blade.php ENDPATH**/ ?>