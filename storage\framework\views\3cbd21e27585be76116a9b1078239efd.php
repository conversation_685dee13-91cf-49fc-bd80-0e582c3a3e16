<?php $__env->startSection('frontend'); ?>
<section class="content">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card card-outline card-danger mt-5">

                    <div class="card-body">
                        <div class="row">
                            <div class="col-12">
                                <p class="text-center" style="font-size: 25px;">Current Applicants Login</p>
                            </div>
                        </div>
                        <form action="<?php echo e(route('edit.application.sendotp')); ?>" method="POST">
                            <?php echo csrf_field(); ?>
                            <div class="form-group">
                                <label for="citizenship">Student Type</label>
                                <select class="form-control" id="citizenship" name="citizenship" required>
                                    <option value="" selected disabled>Select Student Type</option>
                                    <option value="59">Sri Lankan Student</option>
                                    <option value="60">Foreign Student</option>
                                </select>
                                <span class="text-danger"><?php $__errorArgs = ['citizenship'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                            </div>
                            <div class="form-group" id="nicField" style="display: none;" >
                                <label for="nic">NIC</label>
                                <input type="text" class="form-control" id="nic" name="nic">
                                <span class="text-danger"><?php $__errorArgs = ['nic'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                            </div>
                            <div class="form-group" id="passportField" style="display: none;">
                                <label for="passport">Passport Number</label>
                                <input type="text" class="form-control" id="passport" name="passport">
                                <span class="text-danger"><?php $__errorArgs = ['passport'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                            </div>

                            <div class="form-group">
                                <label for="mobile">Mobile</label>
                                <input type="text" class="form-control" id="mobile" name="mobile" required>
                                <span class="text-danger"><?php $__errorArgs = ['mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                                <span class="text-danger"><?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                            </div>
                            <div class="row">
                                <div class="col-md-8 col-sm-12">
                                  <div class="icheck-danger">
                                    <input type="checkbox" id="agreeTerms" name="terms" value="agree">
                                    <label for="agreeTerms">
                                     I agree to receive all information via email</a>
                                    </label>
                                  </div>
                                  <span class="text-danger"><?php $__errorArgs = ['terms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?><?php echo e($message); ?><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?></span>
                                </div>
                                <!-- /.col -->
                                
                                <!-- /.col -->
                              </div>
                            <button type="submit" class="btn btn-danger btn-block">Send OTP to Email</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<script>
    document.getElementById('citizenship').addEventListener('change', function() {
        const nicField = document.getElementById('nicField');
        const passportField = document.getElementById('passportField');
        const nicInput = document.getElementById('nic');
        const passportInput = document.getElementById('passport');

        if (this.value === '59') {
            nicField.style.display = 'block';
            passportField.style.display = 'none';
            nicInput.required = true;
            passportInput.required = false;
            passportInput.value = '';

        } else if (this.value === '60') {
            nicField.style.display = 'none';
            passportField.style.display = 'block';
            nicInput.required = false;
            passportInput.required = true;
            nicInput.value = '';

        } else {
            nicField.style.display = 'none';
            passportField.style.display = 'none';
            nicInput.required = false;
            passportInput.required = false;
        }
    });
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/frontend/taught_application_edit.blade.php ENDPATH**/ ?>