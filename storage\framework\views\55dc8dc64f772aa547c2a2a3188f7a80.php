<?php $__env->startSection('admin'); ?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Manage Subject Areas</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('admin.dashboard')); ?>">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('application.initiate.index')); ?>">Applications</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('application.initiate.research.add')); ?>">Research Initiate</a></li>
                    <li class="breadcrumb-item active">Manage Subjects</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-cogs mr-2"></i>
                            Manage Subject Areas for <?php echo e($course->course_name); ?>

                        </h3>
                        <div class="card-tools">
                            <span class="badge badge-dark">Academic Year: <?php echo e($currentYear); ?></span>
                            <span class="badge badge-info">Study Board: <?php echo e($course->studyBoardName->name ?? 'N/A'); ?></span>
                        </div>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <form method="POST" action="<?php echo e(route('application.initiate.research.update.subjects', $course->id)); ?>">
                            <?php echo csrf_field(); ?>
                            
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle mr-2"></i>
                                <strong>Instructions:</strong>
                                <ul class="mb-0 mt-2">
                                    <li>Select subject areas that should be available for applications</li>
                                    <li>Only subjects with PhD eligibility will be available for selection</li>
                                    <li>Unchecking a subject will deactivate existing applications for that subject</li>
                                    <li>Checking a subject will create or reactivate applications for that subject</li>
                                </ul>
                            </div>

                            <?php if($allSubjects->count() > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th width="5%">
                                                    <input type="checkbox" id="select-all" class="form-check-input">
                                                </th>
                                                <th width="10%">Subject ID</th>
                                                <th width="35%">Subject Name</th>
                                                <th width="20%">Eligibility Status</th>
                                                <th width="15%">Current Status</th>
                                                <th width="15%">Application Status</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $allSubjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php
                                                    $isEligible = $subject->phd_eligiblity == 1;
                                                    $currentApp = $currentOpenApplications->where('subject_code', $subject->id)->first();
                                                    $isCurrentlyActive = $currentApp && $currentApp->active_status == 1;
                                                ?>
                                                <tr class="<?php echo e(!$isEligible ? 'table-secondary' : ''); ?>">
                                                    <td>
                                                        <?php if($isEligible): ?>
                                                            <input type="checkbox" 
                                                                   name="subjects[]" 
                                                                   value="<?php echo e($subject->id); ?>" 
                                                                   class="form-check-input subject-checkbox"
                                                                   <?php echo e($isCurrentlyActive ? 'checked' : ''); ?>>
                                                        <?php else: ?>
                                                            <i class="fas fa-ban text-muted" title="Not eligible for PhD"></i>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td><?php echo e($subject->id); ?></td>
                                                    <td>
                                                        <?php echo e($subject->name); ?>

                                                        <?php if(!$isEligible): ?>
                                                            <small class="text-muted d-block">Not eligible for PhD applications</small>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <div class="eligibility-badges">
                                                            <span class="badge badge-<?php echo e($subject->phd_eligiblity ? 'success' : 'danger'); ?> mr-1">
                                                                PhD: <?php echo e($subject->phd_eligiblity ? 'Yes' : 'No'); ?>

                                                            </span>
                                                            <span class="badge badge-<?php echo e($subject->mphil_eligiblity ? 'success' : 'danger'); ?> mr-1">
                                                                MPhil: <?php echo e($subject->mphil_eligiblity ? 'Yes' : 'No'); ?>

                                                            </span>
                                                            <br>
                                                            <span class="badge badge-<?php echo e($subject->mar_eligiblity ? 'success' : 'danger'); ?> mr-1 mt-1">
                                                                MAR: <?php echo e($subject->mar_eligiblity ? 'Yes' : 'No'); ?>

                                                            </span>
                                                            <span class="badge badge-<?php echo e($subject->mat_eligiblity ? 'success' : 'danger'); ?> mr-1 mt-1">
                                                                MAT: <?php echo e($subject->mat_eligiblity ? 'Yes' : 'No'); ?>

                                                            </span>
                                                            <span class="badge badge-<?php echo e($subject->maq_eligiblity ? 'success' : 'danger'); ?> mt-1">
                                                                MAQ: <?php echo e($subject->maq_eligiblity ? 'Yes' : 'No'); ?>

                                                            </span>
                                                        </div>
                                                    </td>
                                                    <td>
                                                        <?php if($isEligible): ?>
                                                            <span class="badge badge-success">Available</span>
                                                        <?php else: ?>
                                                            <span class="badge badge-secondary">Not Available</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if($currentApp): ?>
                                                            <?php if($currentApp->active_status == 1): ?>
                                                                <span class="badge badge-success">Active</span>
                                                            <?php else: ?>
                                                                <span class="badge badge-warning">Inactive</span>
                                                            <?php endif; ?>
                                                        <?php else: ?>
                                                            <span class="badge badge-light">No Application</span>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>

                                <div class="row mt-4">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <button type="submit" class="btn btn-primary">
                                                <i class="fas fa-save mr-2"></i>Update Subject Areas
                                            </button>
                                            <a href="<?php echo e(route('application.initiate.research.add')); ?>" class="btn btn-secondary ml-2">
                                                <i class="fas fa-arrow-left mr-2"></i>Back to Research Initiate
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-warning">
                                    <i class="fas fa-exclamation-triangle mr-2"></i>
                                    No subjects found for this study board.
                                </div>
                            <?php endif; ?>
                        </form>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
        </div>
    </div>
</section>
<!-- /.content -->

<script>
$(document).ready(function() {
    // Select/Deselect all functionality
    $('#select-all').change(function() {
        var isChecked = $(this).is(':checked');
        $('.subject-checkbox').prop('checked', isChecked);
    });
    
    // Update select-all checkbox based on individual checkboxes
    $('.subject-checkbox').change(function() {
        var totalCheckboxes = $('.subject-checkbox').length;
        var checkedCheckboxes = $('.subject-checkbox:checked').length;
        
        if (checkedCheckboxes === totalCheckboxes) {
            $('#select-all').prop('checked', true);
            $('#select-all').prop('indeterminate', false);
        } else if (checkedCheckboxes === 0) {
            $('#select-all').prop('checked', false);
            $('#select-all').prop('indeterminate', false);
        } else {
            $('#select-all').prop('checked', false);
            $('#select-all').prop('indeterminate', true);
        }
    });
    
    // Initialize select-all state
    $('.subject-checkbox').trigger('change');
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/admin/application/initiate/manage_subjects.blade.php ENDPATH**/ ?>