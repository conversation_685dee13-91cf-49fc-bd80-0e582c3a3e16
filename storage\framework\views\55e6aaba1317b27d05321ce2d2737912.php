<?php $__env->startSection('admin'); ?>
    <!-- Content Header (Page header) -->
    <section class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1>Research Course Application Initiate</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Home</a></li>
                        <li class="breadcrumb-item"><a href="<?php echo e(route('application.initiate.index')); ?>">Applications</a></li>
                        <li class="breadcrumb-item active">Research Initiate</li>
                    </ol>
                </div>
            </div>
        </div>
    </section>

    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <?php if(auth()->guard()->check()): ?>
                        <div class="card card-<?php echo e(auth()->user()->sidebar_color); ?>">
                        <?php endif; ?>
                        <div class="card-header">
                            <h3 class="card-title">
                                <a href="<?php echo e(URL::previous()); ?>">
                                    <i class="fa fa-arrow-circle-left" aria-hidden="true" style="font-size: 30px;"></i>
                                </a>
                                Available Research Courses for Initiation
                            </h3>
                            <div class="card-tools">
                                <span class="badge badge-dark">Academic Year: <?php echo e($currentYear); ?></span>
                            </div>
                        </div>
                        <!-- /.card-header -->
                        <div class="card-body">
                            <?php if($courses->count() > 0): ?>
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped" id="example2">
                                        <thead>
                                            <tr>
                                                <th width="8%">Course Code</th>
                                                <th width="25%">Course Name</th>
                                                <th width="15%">Study Board</th>
                                                <th width="12%">Category</th>
                                                <th width="20%">Available Subjects</th>
                                                <th width="8%">Status</th>
                                                <th width="12%">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <tr>
                                                <td><?php echo e($course->id); ?></td>
                                                <td><?php echo e($course->course_name); ?></td>
                                                <td><?php echo e($course->studyBoardName->name ?? 'N/A'); ?></td>
                                                <td><?php echo e($course->courseCategory->category_name ?? 'N/A'); ?></td>
                                                <td>
                                                    <?php if($course->available_subjects->count() > 0): ?>
                                                        <div class="subjects-list">
                                                            <?php $__currentLoopData = $course->available_subjects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subject): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                <span class="badge badge-primary mr-1 mb-1"><?php echo e($subject->name); ?></span>
                                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                        </div>
                                                        <small class="text-muted"><?php echo e($course->available_subjects->count()); ?> subject(s)</small>
                                                    <?php else: ?>
                                                        <span class="text-muted">No specific subjects</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if($course->active_status == 1): ?>
                                                        <span class="badge badge-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge badge-danger">Inactive</span>
                                                    <?php endif; ?>
                                                    <?php if($course->is_initiated): ?>
                                                        <br><span class="badge badge-info mt-1">Initiated <?php echo e($currentYear); ?></span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('application.initiate.research.create')): ?>
                                                        <?php if($course->is_initiated): ?>
                                                            <button type="button" class="btn btn-sm btn-secondary" disabled>
                                                                <i class="fas fa-check mr-1"></i>Already Initiated
                                                            </button>
                                                            <?php if(in_array($course->course_cat_id, [20, 21, 22, 23, 24])): ?>
                                                                <br>
                                                                <a href="<?php echo e(route('application.initiate.research.manage.subjects', $course->id)); ?>" class="btn btn-sm btn-info mt-1">
                                                                    <i class="fas fa-cogs mr-1"></i>Manage Subjects
                                                                </a>
                                                            <?php endif; ?>
                                                        <?php else: ?>
                                                            <form method="POST" action="<?php echo e(route('application.initiate.research.quick', $course->id)); ?>" style="display: inline-block;">
                                                                <?php echo csrf_field(); ?>
                                                                <button type="submit" class="btn btn-sm btn-success" id="initiate">
                                                                    <i class="fas fa-play mr-1"></i>Initiate
                                                                </button>
                                                            </form>
                                                        <?php endif; ?>
                                                    <?php else: ?>
                                                        <span class="text-muted">No Permission</span>
                                                    <?php endif; ?>
                                                </td>
                                            </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div class="alert alert-info">
                                    <h5><i class="icon fas fa-info"></i> No Courses Available!</h5>
                                    No research courses are currently available for application initiation.
                                </div>
                            <?php endif; ?>
                        </div>
                        <!-- /.card-body -->
                        <div class="card-footer">
                            <div class="row">
                                <div class="col-md-8">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle"></i>
                                        Clicking "Initiate" will create application records for academic year <?php echo e($currentYear); ?> with intake 1.
                                        <br>
                                        <i class="fas fa-check-circle text-info"></i>
                                        Courses already initiated for <?php echo e($currentYear); ?> will show a disabled button.
                                    </small>
                                </div>
                                <div class="col-md-4 text-right">
                                    <a href="<?php echo e(route('application.initiate.index')); ?>" class="btn btn-secondary">
                                        <i class="fas fa-list mr-1"></i>View All Applications
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- /.card -->
                </div>
                <!-- /.col -->
            </div>
            <!-- /.row -->
        </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/admin/application/initiate/add_research.blade.php ENDPATH**/ ?>