<?php $__env->startSection('admin'); ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0"><span class="text-danger"><?php echo e(ucwords($categoryType->name)); ?></span> - Category List</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Home</a></li>
                    <li class="breadcrumb-item active"><a href="<?php echo e(route('category.type.index')); ?>">Category Type List</a></li>
                    <li class="breadcrumb-item active">Category List</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <div class="box-header with-border">
                        <div class="row">
                            <div class="col-md-12">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('category.list')): ?>
                                <?php if(auth()->guard()->check()): ?>
                                <a href="<?php echo e(route('category.index')); ?>" style="float: right;" class="btn bg-<?php echo e(auth()->user()->sidebar_color); ?> mb-5 ml-2">Category List</a>
                                <?php endif; ?>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('category.type.list')): ?>
                                <?php if(auth()->guard()->check()): ?>
                                <a href="<?php echo e(route('category.type.index')); ?>" style="float: right;" class="btn bg-<?php echo e(auth()->user()->sidebar_color); ?> mb-5 ml-2">Category Type List</a>
                                <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <!-- /.box-header -->
                    <div class="box-body">
                        <div class="table-responsive">
                            <table id="example1" class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th width="5%">SN</th>
                                        <th>Category</th>.
                                        <th>Category ID</th>
                                        <th>Category Code</th>
                                        <th width="30%">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($key+1); ?></td>
                                        <td><?php echo e($category->id); ?></td>
								        <td><?php echo e(ucfirst($category->category_name)); ?></td>
                                        <td><?php echo e($category->category_code); ?></td>
                                        <td>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('category.updation')): ?>
                                            <a href="<?php echo e(route('category.edit', $category->id)); ?>" class="btn btn-sm btn-info">Edit <i class="fas fa-pencil-alt"></i></a>
                                            <?php endif; ?>
                                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('category.delete')): ?>
                                            <a href="<?php echo e(route('category.delete', $category->id)); ?>" class="btn btn-sm btn-danger" id="delete">Delete <i class="fas fa-trash"></i></a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- /.box-body -->
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/admin/setups/category_type/list.blade.php ENDPATH**/ ?>