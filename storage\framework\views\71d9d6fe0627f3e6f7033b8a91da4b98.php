<?php $__env->startSection('admin'); ?>
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Taught Course Fee List</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Home</a></li>
                    <li class="breadcrumb-item active">Taught Course Fee</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="box">
                    <!-- /.box-header -->
                    <div class="box-body">
                        <div class="table-responsive">
                            <div class="box-body">
                                <div class="table-responsive">
                                    <table id="example1" class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th>Course Code</th>
                                                <th>Study Board</th>
                                                <th>Course Name</th>
                                                <th>Latest Course Fee Summary</th>
                                                <th width="20%">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php
                                                    // Fetch all course fees ordered by reg_year and batch
                                                    $courseFees = App\Models\CourseFees::where('course_code', $course->id)
                                                        ->where('pay_income_type_id','!=', 1)
                                                        ->where('active_status',1)
                                                        ->orderBy('reg_year', 'desc')
                                                        ->orderBy('batch', 'desc')
                                                        ->orderBy('pay_income_type_id', 'asc')
                                                        ->get();

                                                    // Get the latest reg_year and batch
                                                    $latestCourseFee = $courseFees->first();
                                                    $latestRegYear = $latestCourseFee ? $latestCourseFee->reg_year : null;
                                                    $latestBatch = $latestCourseFee ? $latestCourseFee->batch : null;
                                                    $latestIntake = $latestCourseFee ? $latestCourseFee->intake : null;

                                                    // Filter course fees for the latest reg_year and batch
                                                    $latestFees = $courseFees->where('reg_year', $latestRegYear)
                                                                             ->where('batch', $latestBatch);

                                                    // Calculate the sum of amounts for the latest reg_year and batch
                                                    $latestFeesSum = $latestFees->sum('amount');
                                                ?>
                                                <tr>
                                                    <td><?php echo e($course->id); ?></td>
                                                    <td><?php echo e($course->studyBoardName->name); ?></td>
                                                    <td><?php echo e($course->course_name); ?></td>
                                                    <td>
                                                        <?php if($latestCourseFee): ?>
                                                            <div><strong>Year : </strong> <?php echo e($latestRegYear); ?></div>
                                                            <div><strong>Batch : </strong> <?php echo e($latestBatch); ?></div>
                                                            <div><strong>Intake : </strong> <?php echo e($latestIntake); ?></div>
                                                            <div><strong>Income Types & Amounts : </strong></div>
                                                            <ul>
                                                                <?php $__currentLoopData = $latestFees; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fee): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <li><?php echo e($fee->incomeType->income_type_name); ?> :- Rs. <?php echo e(number_format($fee->amount, 2)); ?></li>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </ul>
                                                            <div><strong>Total Course Fee : </strong> Rs. <?php echo e(number_format($latestFeesSum, 2)); ?></div>
                                                        <?php else: ?>
                                                            <span class="text-danger">No Course Fee Data</span>
                                                        <?php endif; ?>
                                                    </td>
                                                    <td>
                                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('course.fee.show')): ?>
                                                            <a href="<?php echo e(route('course.fee.show', encrypt($course->id))); ?>" class="btn btn-sm btn-primary">
                                                                Course Fee Add &nbsp; <i class="fa fa-plus"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                        </br></br>
                                                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('course.fee.all.show')): ?>
                                                            <a href="<?php echo e(route('course.fee.all.show', encrypt($course->id))); ?>" class="btn btn-sm btn-secondary">
                                                                Course Fee Summary List &nbsp; <i class="fa fa-list"></i>
                                                            </a>
                                                        <?php endif; ?>
                                                    </td>
                                                </tr>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tbody>
                                    </table>
                                </div>
                            </div>

                        </div>
                    </div>
                    <!-- /.box-body -->
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/admin/setups/course_fee/index.blade.php ENDPATH**/ ?>