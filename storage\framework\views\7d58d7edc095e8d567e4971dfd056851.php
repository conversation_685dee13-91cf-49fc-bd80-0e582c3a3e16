<?php $__env->startSection('frontend'); ?>
<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card" style="background-color:#990000">
                        <div class="card-header">
                            <h3 class="card-title text-white"><a href="<?php echo e(route('active.application.list')); ?>"><i class="fa fa-arrow-circle-left" aria-hidden="true" style="font-size: 23px;"></i></a>&nbsp; &nbsp;Applied Application List</h3>
                        </div>
                    </div>
                    <!-- /.box-header -->
                    <div class="card-body">

                        <div class="table-responsive">
                            <table class="table table-bordered table-hover" id="example2">
                                <thead class="bg-light">
                                    <tr>
                                        <th>SN</th>
                                        <th>Ref. No.</th>
                                        <th>Course Name</th>
                                        <th>Closing Date</th>
                                        <th>Status</th>
                                        <th width="10%">Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $applicationList; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $data): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <td><?php echo e($loop->iteration); ?></td>
                                        <td><?php echo e($data->reference_no); ?></td>
                                        <td><?php echo e($data->display_name); ?></td>
                                        <td><?php echo e(\Carbon\Carbon::parse($data->closing_date)->format('Y-F-d')); ?></td>

                                        <td>
                                            <?php if($data->submit_status == 1): ?>
                                            <span class="badge badge-pill badge-danger">Application Draft</span>
                                            <?php elseif($data->submit_status == 2): ?>
                                            <span class="badge badge-pill badge-warning">Application Payment Pending</span>
                                            <?php elseif($data->submit_status == 3): ?>
                                            <span class="badge badge-pill badge-success">Application Completed with payment</span>
                                            <?php endif; ?>
                                        </td>

                                        <td>
                                             <?php if($data->submit_status == 1): ?>
                                            <a href="<?php echo e(route('application.show', ['openAppID' => encrypt($data->open_application_id), 'id' => encrypt($data->applicant_id)])); ?>" class="btn btn-sm btn-danger"><i class="fas fa-mouse-pointer mr-1"></i>Resume</a>
                                            <?php elseif($data->submit_status == 2): ?>
                                            <a href="<?php echo e(route('application.payment', ['openAppID' => encrypt($data->open_application_id), 'id' => encrypt($data->applicant_id)])); ?>" class="btn btn-sm btn-warning"><i class="fas fa-mouse-pointer mr-1"></i>Process Payment</a>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <!-- /.box -->
            </div>
            <!-- /.col -->
        </div>
        <!-- /.row -->
    </div>
    <!-- /.container-fluid -->
</section>
<!-- /.content -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/frontend/application_edit_list.blade.php ENDPATH**/ ?>