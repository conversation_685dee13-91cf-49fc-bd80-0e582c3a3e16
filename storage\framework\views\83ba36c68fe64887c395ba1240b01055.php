<?php $__env->startSection('admin'); ?>
    <!-- Content Header (Page header) -->
    <!-- Main content -->
    <section class="content">
        <div class="container-fluid">
            <div class="row">
                <!-- left column -->
                <div class="col-md-12">
                    <!-- general form elements -->
                    <?php if(auth()->guard()->check()): ?>
                        <div class="card card-<?php echo e(auth()->user()->sidebar_color); ?>">
                        <?php endif; ?>
                        <div class="card-header">
                            <h3 class="card-title"><a href="<?php echo e(URL::previous()); ?>"><i class="fa fa-arrow-circle-left"
                                        aria-hidden="true" style="font-size: 30px;"></i></a> Taught Course Application Initiate Form</h3>
                        </div>
                        <!-- /.card-header -->
                        <!-- form start -->
                        <form method="post" action="<?php echo e(route('application.initiate.taught.store')); ?>">
                            <?php echo csrf_field(); ?>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="">Course<span class="text-danger"> *</span></label>
                                            <select name="course_code" id="course_code" class="select2bs4"
                                                style="width: 100%">
                                                <option value="" selected disabled>Select Course</option>
                                                <?php $__currentLoopData = $courses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $course): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e($course->id); ?>"
                                                        <?php echo e($course->id == old('course_code') ? 'selected' : ''); ?>>
                                                        <?php echo e($course->course_name); ?> - <?php echo e($course->studyBoardName->name); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                            <span class="text-danger">
                                                <?php $__errorArgs = ['course_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <?php echo e($message); ?>

                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </span>
                                        </div>
                                    </div><!-- col-md-12 -->

                                </div><!-- row -->
                                <div class="row">
                                    <div class="col-md-4">
                                        <label for="">Academic Year<span class="text-danger"> *</span></label>
                                        <div class="form-group">
                                            <input type="number" class="form-control" id="reg_year"
                                                name="reg_year" value="<?php echo e(old('reg_year')); ?>">
                                            <span class="text-danger">
                                                <?php $__errorArgs = ['reg_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <?php echo e($message); ?>

                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </span>
                                        </div>
                                    </div><!-- col-md-12 -->
                                    <div class="col-md-4">
                                        <div class="form-group">
                                            <label for="">Batch<span class="text-danger"> *</span></label>
                                            <input type="number" class="form-control" id="batch"
                                                name="batch" value="<?php echo e(old('batch')); ?>">
                                            <span class="text-danger">
                                                <?php $__errorArgs = ['batch'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <?php echo e($message); ?>

                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </span>

                                        </div>
                                    </div><!-- col-md-12 -->
                                    <div class="col-md-4">
                                        <label for="">Intake<span class="text-danger"> *</span></label>
                                        <div class="form-group">
                                            <input type="text" class="form-control" id="intake" name="intake"
                                                value="<?php echo e(old('intake')); ?>">
                                                <span class="text-danger">
                                                    <?php $__errorArgs = ['intake'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <?php echo e($message); ?>

                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </span>
                                        </div>
                                    </div><!-- col-md-12 -->
                                </div><!-- row -->
                                <div class="row">
                                    <div class="col-md-6">
                                        <label for="">Application Start Date<span class="text-danger"> *</span></label>
                                        <div class="input-group date" id="start_date" data-target-input="nearest">
                                            <input type="text" class="form-control datetimepicker-input" data-target="#start_date" name="start_date" value="<?php echo e(old('start_date')); ?>" placeholder="Ex:- 01-Jan-2000" data-target="#start_date" data-toggle="datetimepicker">
                                            <div class="input-group-append" data-target="#start_date" data-toggle="datetimepicker">
                                                <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                            </div>
                                        </div>
                                        <span class="text-danger">
                                            <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <?php echo e($message); ?>

                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </span>
                                    </div><!-- col-md-4 -->
                                    <div class="col-md-6">
                                        <label for="">Application End Date<span class="text-danger"> *</span></label>
                                        <div class="input-group date" id="end_date" data-target-input="nearest">
                                            <input type="text" class="form-control datetimepicker-input" data-target="#end_date" name="closing_date" value="<?php echo e(old('closing_date')); ?>" placeholder="Ex:- 01-Jan-2000" data-target="#end_date" data-toggle="datetimepicker">
                                            <div class="input-group-append" data-target="#end_date" data-toggle="datetimepicker">
                                                <div class="input-group-text"><i class="fa fa-calendar"></i></div>
                                            </div>
                                        </div>
                                        <span class="text-danger">
                                            <?php $__errorArgs = ['closing_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                <?php echo e($message); ?>

                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </span>
                                    </div><!-- col-md-4 -->
                                </div><!-- row -->

                            </div>
                            <!-- /.card-body -->
                            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('application.initiate.taught.create')): ?>
                                <div class="card-footer">
                                    <?php if(auth()->guard()->check()): ?>
                                        <input type="submit" class="btn bg-<?php echo e(auth()->user()->sidebar_color); ?>" value="Create">
                                    <?php endif; ?>
                                </div>
                            <?php endif; ?>
                        </form>
                    </div>
                    <!-- /.card -->
                </div>
                <!--/.col (left) -->
            </div>
            <!-- /.row -->
        </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/admin/application/initiate/add.blade.php ENDPATH**/ ?>