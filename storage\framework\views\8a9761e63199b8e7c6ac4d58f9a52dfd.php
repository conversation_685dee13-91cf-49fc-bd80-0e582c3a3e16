<?php if(auth()->guard()->check()): ?>
<nav class="main-header navbar navbar-expand <?php echo e(auth()->user()->dark_mode == 1 ? 'navbar-dark' : auth()->user()->nav_color); ?> navbar-light ">
<?php endif; ?>
    <!-- Left navbar links -->
    <ul class="navbar-nav">
      <li class="nav-item">
        <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
      </li>
      <li class="nav-item d-none d-sm-inline-block">
      </li>
      <li class="nav-item d-none d-sm-inline-block">

      </li>

      <li class="nav-item d-none d-sm-inline-block">
        <?php if(auth()->guard()->check()): ?>
        <a href="<?php echo e(route('home')); ?>" class="nav-link" target="_blank">Application</a>
        <?php endif; ?>
      </li>
      <?php if (\Illuminate\Support\Facades\Blade::check('role', 'Super-Admin')): ?>
      <li class="nav-item d-none d-sm-inline-block">
        <a href="<?php echo e(url('/log-viewer')); ?>" class="nav-link" target="_blank">Log Viwer</a>
      </li>
      <?php endif; ?>
      <li class="nav-item d-none d-sm-inline-block">

      </li>

    </ul>

    <!-- Right navbar links -->
    <ul class="navbar-nav ml-auto">
      <!-- Navbar Search -->


      <!-- Messages Dropdown Menu -->

      <!-- Notifications Dropdown Menu -->
      
      <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('site.setting.index')): ?>
      <li class="nav-item">
        <a class="nav-link" href="<?php echo e(route('site.setting.index')); ?>" role="button">
          <i class="fa fa-cogs"></i>
        </a>
      </li>
      <?php endif; ?>
      <li class="nav-item">
        <a class="nav-link" data-widget="fullscreen" href="#" role="button">
          <i class="fas fa-expand-arrows-alt"></i>
        </a>
      </li>

      <li class="nav-item">
        <a class="nav-link" data-widget="navbar-search" href="#" role="button">
          <i class="fas fa-search" style="font-size: 20px;"></i>
        </a>
        <div class="navbar-search-block">
          <form class="form-inline">
            <div class="input-group input-group-sm">
              <input class="form-control form-control-navbar" type="search" placeholder="Search" aria-label="Search">
              <div class="input-group-append">
                <button class="btn btn-navbar" type="submit">
                  <i class="fas fa-search"></i>
                </button>
                <button class="btn btn-navbar" type="button" data-widget="navbar-search">
                  <i class="fas fa-times"></i>
                </button>
              </div>
            </div>
          </form>
        </div>
      </li>

      
    </ul>
  </nav>
<?php /**PATH D:\Development\FGS\resources\views/admin/body/header.blade.php ENDPATH**/ ?>