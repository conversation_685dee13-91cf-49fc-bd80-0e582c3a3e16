<?php $__env->startSection('admin'); ?>

<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Edit Application Initiation</h1>
            </div><!-- /.col -->
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>">Home</a></li>
                    <li class="breadcrumb-item"><a href="<?php echo e(route('application.initiate.index')); ?>">Applications</a></li>
                    <li class="breadcrumb-item active">Edit Application</li>
                </ol>
            </div><!-- /.col -->
        </div><!-- /.row -->
    </div><!-- /.container-fluid -->
</div>
<!-- /.content-header -->

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">
                            <a href="<?php echo e(URL::previous()); ?>">
                                <i class="fa fa-arrow-circle-left" aria-hidden="true" style="font-size: 30px;"></i>
                            </a>
                            Edit Application Initiation
                        </h3>
                    </div>
                    <!-- /.card-header -->
                    <div class="card-body">
                        <form method="POST" action="<?php echo e(route('application.initiate.update', $application->id)); ?>">
                            <?php echo csrf_field(); ?>

                            <!-- Application Information -->
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="alert alert-info">
                                        <h5><i class="icon fas fa-info"></i> Application Information</h5>
                                        <strong>Course:</strong> <?php echo e($application->course->course_name ?? 'N/A'); ?><br>
                                        <strong>Study Board:</strong> <?php echo e($application->course->studyBoardName->name ?? 'N/A'); ?><br>
                                        <strong>Current Display Name:</strong> <?php echo e($application->display_name ?? 'N/A'); ?>

                                    </div>
                                </div>
                            </div>

                            <!-- Basic Information -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="reg_year">Registration Year <span class="text-danger">*</span></label>
                                        <input type="number"
                                               class="form-control <?php $__errorArgs = ['reg_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="reg_year"
                                               name="reg_year"
                                               value="<?php echo e(old('reg_year', $application->reg_year)); ?>"
                                               min="2020"
                                               max="2030"
                                               required>
                                        <?php $__errorArgs = ['reg_year'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="batch">Batch <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control <?php $__errorArgs = ['batch'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="batch" name="batch" value="<?php echo e(old('batch', $application->batch)); ?>"
                                               min="1">
                                        <?php $__errorArgs = ['batch'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="intake">Intake <span class="text-danger">*</span></label>
                                        <input type="number" class="form-control <?php $__errorArgs = ['intake'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" id="intake" name="intake" value="<?php echo e(old('intake', $application->intake)); ?>" min="1">
                                        <?php $__errorArgs = ['intake'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="display_name">Display Name</label>
                                        <input type="text"
                                               class="form-control <?php $__errorArgs = ['display_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="display_name"
                                               name="display_name"
                                               value="<?php echo e(old('display_name', $application->display_name)); ?>"
                                               maxlength="255" readonly>
                                        <?php $__errorArgs = ['display_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="form-text text-muted">Leave blank to auto-generate based on course and subject</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Date Information -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="start_date">Start Date <span class="text-danger">*</span></label>
                                        <input type="date"
                                               class="form-control <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="start_date"
                                               name="start_date"
                                               value="<?php echo e(old('start_date', $application->start_date)); ?>"
                                               required>
                                        <?php $__errorArgs = ['start_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="closing_date">Closing Date <span class="text-danger">*</span></label>
                                        <input type="date"
                                               class="form-control <?php $__errorArgs = ['closing_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="closing_date"
                                               name="closing_date"
                                               value="<?php echo e(old('closing_date', $application->closing_date)); ?>"
                                               required>
                                        <?php $__errorArgs = ['closing_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                            </div>

                            <!-- Deadline Extension Information -->
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="deadline_extented_status">Deadline Extension Status</label>
                                        <select class="form-control <?php $__errorArgs = ['deadline_extented_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                                id="deadline_extented_status"
                                                name="deadline_extented_status">
                                            <option value="0" <?php echo e(old('deadline_extented_status', $application->deadline_extented_status) == 0 ? 'selected' : ''); ?>>
                                                No Extension
                                            </option>
                                            <option value="1" <?php echo e(old('deadline_extented_status', $application->deadline_extented_status) == 1 ? 'selected' : ''); ?>>
                                                Extended
                                            </option>
                                        </select>
                                        <?php $__errorArgs = ['deadline_extented_status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label for="deadline_extented_date">Extended Deadline Date</label>
                                        <input type="date"
                                               class="form-control <?php $__errorArgs = ['deadline_extented_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                               id="deadline_extented_date"
                                               name="deadline_extented_date"
                                               value="<?php echo e(old('deadline_extented_date', $application->deadline_extented_date)); ?>">
                                        <?php $__errorArgs = ['deadline_extented_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback"><?php echo e($message); ?></span>
                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        <small class="form-text text-muted">Only required if deadline is extended</small>
                                    </div>
                                </div>
                            </div>

                            <!-- Status Information -->

                            <!-- Form Actions -->
                            <div class="row mt-4">
                                <div class="col-md-12">
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('application.initiate.updation')): ?>
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-save mr-2"></i>Update Application
                                        </button>
                                    <?php endif; ?>
                                    <a href="<?php echo e(route('application.initiate.index')); ?>" class="btn btn-secondary ml-2">
                                        <i class="fas fa-times mr-2"></i>Cancel
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                    <!-- /.card-body -->
                </div>
                <!-- /.card -->
            </div>
        </div>
    </div>
</section>
<!-- /.content -->

<script>
$(document).ready(function() {
    // Show/hide extended deadline date based on extension status
    $('#deadline_extented_status').change(function() {
        if ($(this).val() == '1') {
            $('#deadline_extented_date').prop('required', true);
            $('#deadline_extented_date').closest('.form-group').show();
        } else {
            $('#deadline_extented_date').prop('required', false);
            $('#deadline_extented_date').val('');
        }
    });

    // Initialize on page load
    $('#deadline_extented_status').trigger('change');

    // Validate closing date is after start date
    $('#start_date, #closing_date').change(function() {
        var startDate = $('#start_date').val();
        var closingDate = $('#closing_date').val();

        if (startDate && closingDate && new Date(closingDate) <= new Date(startDate)) {
            alert('Closing date must be after start date');
            $('#closing_date').focus();
        }
    });
});
</script>

<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/admin/application/initiate/edit.blade.php ENDPATH**/ ?>