<?php $__env->startSection('admin'); ?>

<!-- Content Header (Page header) -->
    <div class="content-header">
      <div class="container-fluid">
        <div class="row mb-2">
          <div class="col-sm-6">
            <h1 class="m-0">Dashboard</h1>

          </div><!-- /.col -->
          <div class="col-sm-6">
            <ol class="breadcrumb float-sm-right">
              <li class="breadcrumb-item"><a href="#">Home</a></li>
              <li class="breadcrumb-item active">Dashboard</li>
            </ol>
          </div><!-- /.col -->
        </div><!-- /.row -->
      </div><!-- /.container-fluid -->
    </div>
    <!-- /.content-header -->

    <!-- Main content -->
    <style>

    </style>
    <br>
    <section class="content">
      <div class="container-fluid">
        <!-- <div class="alert alert-danger" id="success-danger">
          <button type="button" class="close text-white" data-dismiss="alert">x</button>
          <strong>Notice!&nbsp;&nbsp;</strong>
          USJNet Services Tempory unavailable.in case department head functionality may have some dealy.
       </div> -->
        <div class="row">

            <div class="col-lg-3 col-6">
                <!-- small box -->
                <?php if(auth()->guard()->check()): ?>
                <div class="small-box bg-<?php echo e(auth()->user()->sidebar_color); ?>">
                <?php endif; ?>
                  <div class="inner">
                    <h3><?php echo e($StudyBoards); ?></h3>

                    <p>Study Board </p>
                  </div>
                  <div class="icon bg-white">
                    <i class="ion ion-ios-people"></i>
                  </div>
                  <a href="" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
                </div>
            </div>

          <div class="col-lg-3 col-6">
            <!-- small box -->
            <?php if(auth()->guard()->check()): ?>
                <div class="small-box bg-<?php echo e(auth()->user()->sidebar_color); ?>">
                <?php endif; ?>
              <div class="inner">
                <h3><?php echo e($StudyBoardSubjects); ?></h3>

                <p>Study Board Subjects</p>
              </div>
              <div class="icon bg-white">
                <i class="ion ion-checkmark-round"></i>
              </div>
              <a href="" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

          <!-- ./col -->
          <div class="col-lg-3 col-6">
            <!-- small box -->
            <?php if(auth()->guard()->check()): ?>
                <div class="small-box bg-<?php echo e(auth()->user()->sidebar_color); ?>">
                <?php endif; ?>
              <div class="inner">
                <h3><?php echo e($ResearchCourses); ?></h3>

                <p>Research Courses</p>
              </div>
              <div class="icon bg-white">
                <i class="ion ion-clipboard"></i>
              </div>
              <a href="" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>

          <div class="col-lg-3 col-6">
            <!-- small box -->
            <?php if(auth()->guard()->check()): ?>
            <div class="small-box bg-<?php echo e(auth()->user()->sidebar_color); ?>">
            <?php endif; ?>
              <div class="inner">
                <h3><?php echo e($TaughtCourses); ?></h3>

                <p>Taught Courses</p>
              </div>
              <div class="icon bg-white">
                <i class="ion ion-compose"></i>
              </div>
              <a href="" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
            </div>
          </div>



          <!-- ./col -->
        </div>
        <!-- /.row -->
        <div class="row">

            <div class="col-lg-3 col-6">
                <!-- small box -->
                <?php if(auth()->guard()->check()): ?>
                <div class="small-box bg-<?php echo e(auth()->user()->sidebar_color); ?>">
                <?php endif; ?>
                  <div class="inner">
                    <h3><?php echo e($registerdStudents); ?></h3>

                    <p>Active Registered Students</p>
                  </div>
                  <div class="icon bg-white">
                    <i class="ion ion-arrow-graph-up-right"></i>
                  </div>
                  <a href="" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
                </div>
              </div>
              <!-- ./col -->
              <div class="col-lg-3 col-6">
                <!-- small box -->
                <?php if(auth()->guard()->check()): ?>
                <div class="small-box bg-<?php echo e(auth()->user()->sidebar_color); ?>">
                <?php endif; ?>
                  <div class="inner">
                    <h3><?php echo e($registrationPendingStudent); ?></h3>

                    <p>Registration Pending Students</p>
                  </div>
                  <div class="icon bg-white">
                    <i class="ion ion-load-a"></i>
                  </div>
                  <a href="" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
                </div>
              </div>
            <!-- ./col -->
            <div class="col-lg-3 col-6">
                <!-- small box -->
                <?php if(auth()->guard()->check()): ?>
                <div class="small-box bg-<?php echo e(auth()->user()->sidebar_color); ?>">
                <?php endif; ?>
                  <div class="inner">
                    <h3><?php echo e($applicationOpenCourses); ?></h3>

                    <p>Application Active Courses</p>
                  </div>
                  <div class="icon bg-white">
                    <i class="ion ion-trophy"></i>
                  </div>
                  <a href="" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
                </div>
              </div>
            <div class="col-lg-3 col-6">
              <!-- small box -->
              <?php if(auth()->guard()->check()): ?>
                <div class="small-box bg-<?php echo e(auth()->user()->sidebar_color); ?>">
                <?php endif; ?>
                <div class="inner">
                  <h3><?php echo e($systemUsers); ?></h3>

                  <p>System Active Users</p>
                </div>
                <div class="icon bg-white">
                  <i class="ion ion-trash-b"></i>
                </div>
                <a href="" class="small-box-footer">More info <i class="fas fa-arrow-circle-right"></i></a>
              </div>
            </div>
            <!-- ./col -->
            

            <!-- ./col -->
          </div>
        <!-- /.row -->

        <!-- Main row -->

        <!-- /.row (main row) -->
      </div><!-- /.container-fluid -->
    </section>
    <!-- /.content -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make('admin.admin_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/admin/index.blade.php ENDPATH**/ ?>