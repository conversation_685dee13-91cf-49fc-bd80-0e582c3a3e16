<?php $__env->startSection('frontend'); ?>
    <div class="application-wrapper">
        <!-- Header Section -->
        <section class="content-header mb-2">
            <div class="container-fluid">
                <div class="row">
                    <div class="col-12">
                        <h3 class="text-center">Application for <?php echo e($courseData->display_name); ?></h3>
                    </div>
                </div>
            </div>
        </section>

        <!-- Main Application Form -->
        <section class="content">
            <div class="container">
                <?php if(session('success')): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo e(session('success')); ?>

                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <?php if(session('error')): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo e(session('error')); ?>

                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <?php if($errors->any()): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <h6 class="mb-3">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Validation Failed!</strong>
                            Please correct the following <?php echo e($errors->count()); ?> error<?php echo e($errors->count() > 1 ? 's' : ''); ?>:
                        </h6>
                        
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                <?php endif; ?>

                <form action="<?php echo e(route('application.data.submit')); ?>" method="POST" enctype="multipart/form-data"
                    id="applicationForm">
                    <?php echo csrf_field(); ?>
                    <div class="row">
                        <div class="col-lg-3 col-md-4 sticky-nav-container">
                            <div class="sticky-nav">
                                <!-- Mobile toggle button -->
                                <button class="btn btn-danger d-md-none w-100 mb-3" type="button" data-toggle="collapse" data-target="#sectionNavCollapse" aria-expanded="false" aria-controls="sectionNavCollapse">
                                    <i class="fas fa-bars"></i>
                                </button>

                                <div class="collapse d-md-block" id="sectionNavCollapse">
                                    <?php echo $__env->make('frontend.application.section-nav', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-9 col-md-8">
                            <div class="card">
                                <div class="card-body">
                                    <!-- Personal Information -->
                                    <div class="section-block" id="personal-info-section">
                                        <h5 class="section-title"><b>Personal Information</b></h5>
                                        <div class="row">
                                            <input type="hidden" name="open_application_id" value="<?php echo e($CourseOpenId); ?>">
                                            <input type="hidden" name="applicant_id" value="<?php echo e($applicationId); ?>">
                                            <input type="hidden" name="course_main_category" value="<?php echo e($courseData->course_main_category); ?>">

                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Title <span class="required">*</span></label>
                                                    <select name="title_id" class="form-control">
                                                        <option value="">Select Title</option>
                                                        <?php $__currentLoopData = $titles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $title): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($title->id); ?>"
                                                                <?php echo e(old('title_id', $application->title_id ?? '') == $title->id ? 'selected' : ''); ?>>
                                                                <?php echo e($title->category_name); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['title_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Initials (e.g., A.B.C.) <span class="required">*</span></label>
                                                    <input type="text" name="initials" class="form-control"
                                                        value="<?php echo e(old('initials', $application->initials ?? '')); ?>">
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['initials'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Name Denoted by Initials <span class="required">*</span></label>
                                                    <input type="text" name="name_denoted_by_initials"
                                                        class="form-control" value="<?php echo e(old('name_denoted_by_initials', $application->name_denoted_by_initials ?? '')); ?>">
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['name_denoted_by_initials'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Last Name <span class="required">*</span></label>
                                                    <input type="text" name="last_name" class="form-control"
                                                        value="<?php echo e(old('last_name', $application->last_name ?? '')); ?>">
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['last_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <?php if($application->citizenship_type == '59'): ?>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>NIC <span class="required">*</span></label>
                                                        <?php if($application->active_nic == 1): ?>
                                                        <input type="text" name="nic" class="form-control"
                                                            value="<?php echo e($application->old_nic); ?>" readonly>
                                                        <?php elseif($application->active_nic == 2): ?>
                                                         <input type="text" name="nic" class="form-control"
                                                            value="<?php echo e($application->new_nic); ?>" readonly>
                                                        <?php endif; ?>

                                                    </div>
                                                </div>
                                            <?php else: ?>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Passport Number <span class="required">*</span></label>
                                                        <input type="text" name="passport" class="form-control"
                                                            value="<?php echo e($application->passport); ?>" readonly>
                                                    </div>
                                            <?php endif; ?>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Date of Birth <span class="required">*</span></label>
                                                    <input type="date" name="dob" class="form-control"
                                                        value="<?php echo e(old('dob', $application->dob ?? '')); ?>">
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['dob'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>

                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Gender <span class="required">*</span></label>
                                                    <select name="gender_cat_id" class="form-control">
                                                        <option value="">Select Gender</option>
                                                        <?php $__currentLoopData = $genders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gender): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($gender->id); ?>"
                                                                <?php echo e(old('gender_cat_id', $application->gender_cat_id ?? '') == $gender->id ? 'selected' : ''); ?>>
                                                                <?php echo e($gender->category_name); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['gender_cat_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Civil Status <span class="required">*</span></label>
                                                    <select name="civil_status_cat_id" class="form-control">
                                                        <option value="">Select Civil Status</option>
                                                        <?php $__currentLoopData = $civilStatuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $civilStatus): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($civilStatus->id); ?>"
                                                                <?php echo e(old('civil_status_cat_id', $application->civil_status_cat_id ?? '') == $civilStatus->id ? 'selected' : ''); ?>>
                                                                <?php echo e($civilStatus->category_name); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['civil_status_cat_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>

                                        </div>
                                        <!-- Continue with other personal information fields -->
                                    </div>

                                    <!-- Contact Information -->
                                    <div class="section-block mt-4" id="contact-info-section">
                                        <h5 class="section-title"><b>Contact Information</b></h5>

                                        <!-- Permanent Address -->
                                        <div class="address-block mb-4">
                                            <h6>Permanent Address</h6>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Address Line 1 <span class="required">*</span></label>
                                                        <input type="text" name="permanent_address1"
                                                            class="form-control" value="<?php echo e(old('permanent_address1', $application->permanent_add1 ?? '')); ?>">
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['permanent_address1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Address Line 2</label>
                                                        <input type="text" name="permanent_address2"
                                                            class="form-control" value="<?php echo e(old('permanent_address2', $application->permanent_add2 ?? '')); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Address Line 3</label>
                                                        <input type="text" name="permanent_address3"
                                                            class="form-control" value="<?php echo e(old('permanent_address3', $application->permanent_add3 ?? '')); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>City <span class="required">*</span></label>
                                                        <input type="text" name="permanent_city" class="form-control" value="<?php echo e(old('permanent_city', $application->permanent_city_id ?? '')); ?>">
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['permanent_city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Postal Code <span class="required">*</span></label>
                                                        <input type="text" name="permanent_postal_code"
                                                            class="form-control" value="<?php echo e(old('permanent_postal_code', $application->permanent_postal_code ?? '')); ?>">
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['permanent_postal_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Postal Address -->
                                        <div class="address-block mb-4">
                                            <h6>Postal Address</h6>
                                            <div class="row">
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Address Line 1 <span class="required">*</span></label>
                                                        <input type="text" name="postal_address1"
                                                            class="form-control" value="<?php echo e(old('postal_address1', $application->postal_add1 ?? '')); ?>">
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['postal_address1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Address Line 2</label>
                                                        <input type="text" name="postal_address2"
                                                            class="form-control" value="<?php echo e(old('postal_address2', $application->postal_add2 ?? '')); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-4">
                                                    <div class="form-group">
                                                        <label>Address Line 3</label>
                                                        <input type="text" name="postal_address3"
                                                            class="form-control" value="<?php echo e(old('postal_address3', $application->postal_add3 ?? '')); ?>">
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>City <span class="required">*</span></label>
                                                        <input type="text" name="postal_city" class="form-control" value="<?php echo e(old('postal_city', $application->postal_city_id ?? '')); ?>">
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['postal_city'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                        </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Postal Code <span class="required">*</span></label>
                                                        <input type="text" name="postal_postal_code"
                                                            class="form-control" value="<?php echo e(old('postal_postal_code', $application->postal_postal_code ?? '')); ?>">
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['postal_postal_code'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                                <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Mobile Number <span class="required">*</span></label>
                                                    <input type="tel" name="mobile_no" class="form-control"
                                                        value="<?php echo e($application->mobile_no); ?>" readonly>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Email Address <span class="required">*</span></label>
                                                    <input type="email" name="email" class="form-control"
                                                        value="<?php echo e($application->email); ?>" readonly>
                                                </div>
                                            </div>

                                        </div>

                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Home Telephone Number</label>
                                                    <input type="tel" name="tel_home" class="form-control" value="<?php echo e(old('tel_home', $application->tel_home ?? '')); ?>">
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Office Telephone Number</label>
                                                    <input type="tel" name="tel_office" class="form-control" value="<?php echo e(old('tel_office', $application->tel_office ?? '')); ?>">
                                                </div>
                                            </div>

                                        </div>
                                    </div>



                                    <!-- First Degree Details -->
                                    <div class="section-block mt-4" id="first-degree-section">
                                        <h5 class="section-title"><b>First Degree Details</b></h5>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>First Degree Name <span class="required">*</span></label>
                                                    <input type="text" name="first_degree_name" class="form-control" value="<?php echo e(old('first_degree_name', $application->first_degree_name ?? '')); ?>">
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['first_degree_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Major Subjects <span class="required">*</span></label>
                                                    <input type="text" name="first_degree_major_subject" class="form-control" value="<?php echo e(old('first_degree_major_subject', $application->major_subject ?? '')); ?>">
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['first_degree_major_subject'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>University <span class="required">*</span></label>
                                                    <input type="text" name="first_degree_university" class="form-control" value="<?php echo e(old('first_degree_university', $application->university ?? '')); ?>">
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['first_degree_university'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-group">
                                                    <label>Class Category <span class="required">*</span></label>
                                                    <select name="class_cat_id" class="form-control">
                                                        <option value="">Select Class Category</option>
                                                        <?php $__currentLoopData = $degreeClassTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $degreeClassType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                            <option value="<?php echo e($degreeClassType->id); ?>"
                                                                <?php echo e(old('class_cat_id', $application->class_cat_id ?? '') == $degreeClassType->id ? 'selected' : ''); ?>>
                                                                <?php echo e($degreeClassType->category_name); ?></option>
                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                    </select>
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['class_cat_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label>Effective Date <span class="required">*</span></label>
                                                    <input type="date" name="first_degree_effective_date" class="form-control" value="<?php echo e(old('first_degree_effective_date', $application->effective_date ?? '')); ?>">
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['first_degree_effective_date'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label>Duration (Years) <span class="required">*</span></label>
                                                    <input type="number" name="first_degree_duration" class="form-control" value="<?php echo e(old('first_degree_duration', $application->duration ?? '')); ?>">
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['first_degree_duration'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>
                                            <div class="col-md-4">
                                                <div class="form-group">
                                                    <label>GPA <span class="required">*</span></label>
                                                    <input type="number" name="gpa" class="form-control"
                                                        step="0.01" min="0" max="4.0" value="<?php echo e(old('gpa', $application->gpa ?? '')); ?>">
                                                    <span class="text-danger">
                                                        <?php $__errorArgs = ['gpa'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Other Academic Qualifications -->
                                    <div class="section-block mt-4" id="qualifications-section">
                                        <h5 class="section-title"><b>Qualifications</b></h5>


                                        <!-- Qualification Details Container -->
                                        <div id="qualifications-container">
                                            <!-- Initial Qualification Block -->
                                            <div class="qualification-block mt-3">
                                                <h6>Qualification Details</h6>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Qualification Type <span
                                                                    class="required">*</span></label>
                                                            <select name="qualification_type_id" id="qualification_type_id" class="form-control">
                                                                <option value="">Select Qualification Type</option>
                                                                <?php $__currentLoopData = $qualificationTypes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $qualificationType): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                    <option
                                                                        value="<?php echo e($qualificationType->id); ?>">
                                                                        <?php echo e($qualificationType->category_name); ?></option>
                                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Qualification Name <span
                                                                    class="required">*</span></label>
                                                            <input type="text" name="qulification_name" id="qulification_name"
                                                                class="form-control">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>University/Institution <span
                                                                    class="required">*</span></label>
                                                            <input type="text" name="qualification_university" id="qualification_university" class="form-control">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Effective Date <span class="required">*</span></label>
                                                            <input type="date" name="qualification_effective_date" id="qualification_effective_date"
                                                                class="form-control">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Duration (Years) <span class="required">*</span></label>
                                                            <input type="number" name="qualification_duration" id="qualification_duration" class="form-control"
                                                                min="1">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Major Subject </label>
                                                            <input type="text" name="qualification_major_subject" id="qualification_major_subject"
                                                                class="form-control">
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Add Qualification Button -->
                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <button type="button" class="btn btn-outline-danger btn-sm"
                                                    id="add-qualification">
                                                    <i class="fas fa-plus"></i> Add Qualification
                                                </button>
                                            </div>
                                        </div>
                                        <hr>


                                        <div class="table-container">
                                            <table class="table table-bordered" id="qualifications-table">
                                            <thead>
                                                <tr>
                                                    <th>Type</th>
                                                    <th>Name</th>
                                                    <th>University</th>
                                                    <th>Effective Date</th>
                                                    <th>Duration</th>
                                                    <th>Major Subject</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $qualifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $qualification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($qualification->qualificationType->category_name); ?></td>
                                                        <td><?php echo e($qualification->qulification_name); ?></td>
                                                        <td><?php echo e($qualification->university); ?></td>
                                                        <td><?php echo e($qualification->effective_date->format('Y-m-d')); ?></td>
                                                        <td><?php echo e($qualification->duration); ?> years</td>
                                                        <td><?php echo e($qualification->major_subject); ?></td>
                                                        <td>
                                                            <a href="<?php echo e(route('application.qualification.delete', $qualification->id)); ?>" class="btn btn-sm btn-danger">Delete</a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <!-- Employment Details -->
                                    <div class="section-block mt-4" id="employment-section">
                                        <h5 class="section-title"><b>Employment Details</b></h5>
                                        <div class="employment-records">
                                            <!-- Single Employment Record -->
                                            <div class="employment-record">
                                                <div class="row">
                                                     <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Working Status <span class="required">*</span></label>
                                                            <select name="working_status" id="working_status" class="form-control">
                                                                <option value="">Select Working Status</option>
                                                                <option value="1">Current Working</option>
                                                                <option value="2">Pervious Working</option>
                                                            </select>
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Employer <span class="required">*</span></label>
                                                            <input type="text" name="institution"
                                                                class="form-control">
                                                        </div>
                                                    </div>

                                                </div>
                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Address <span class="required">*</span></label>
                                                            <input type="text" name="emp_address"
                                                                class="form-control">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Position/Rank <span class="required">*</span></label>
                                                            <input type="text" name="position" class="form-control">
                                                        </div>
                                                    </div>

                                                </div>

                                                <div class="row">
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Period <span class="required">*</span></label>
                                                            <input type="text" name="period" class="form-control"
                                                                placeholder="e.g., 2020-2023">
                                                        </div>
                                                    </div>
                                                    <div class="col-md-6">
                                                        <div class="form-group">
                                                            <label>Nature of Duty <span class="required">*</span></label>
                                                            <input type="text" name="nature_of_duty"
                                                                class="form-control">
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                        <!-- Add More Employment Records Button -->
                                        <div class="row mt-3">
                                            <div class="col-12">
                                                <button type="button"
                                                    class="btn btn-outline-danger btn-sm add-employment-record">
                                                    <i class="fas fa-plus"></i> Add Employment Record
                                                </button>
                                            </div>
                                        </div>
                                        <hr>


                                        <div class="table-container">
                                            <table class="table table-bordered" id="employee-record-table">
                                            <thead>
                                                <tr>
                                                    <th>Employer</th>
                                                    <th>Address</th>
                                                    <th>Position/Rank</th>
                                                    <th>Period</th>
                                                    <th>Nature of Duty</th>
                                                    <th>working Status</th>
                                                    <th>Action</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php $__currentLoopData = $employeeRecords; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $employeeRecord): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <tr>
                                                        <td><?php echo e($employeeRecord->employer); ?></td>
                                                        <td><?php echo e($employeeRecord->address); ?></td>
                                                        <td><?php echo e($employeeRecord->position); ?></td>
                                                        <td><?php echo e($employeeRecord->period); ?></td>
                                                        <td><?php echo e($employeeRecord->nature_of_duty); ?></td>
                                                        <td><?php echo e($employeeRecord->working_status == 1 ? 'Current Working' : 'Previous Working'); ?></td>
                                                        <td>
                                                            <a href="<?php echo e(route('application.employment.delete', $employeeRecord->id)); ?>" class="btn btn-sm btn-danger">Delete</a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <?php if($courseData->course_main_category == 42): ?>
                                    <!-- Research Proposal Title -->
                                    <div class="section-block mt-4" id="research-title-section">
                                        <h5 class="section-title"><b>Research Proposal Title</b></h5>
                                        <div class="employment-records">
                                            <!-- Single Employment Record -->
                                            <div class="employment-record">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="form-group">
                                                            <label>Research Proposal Title <span class="required">*</span></label>
                                                            <input type="text" name="proposal_title" class="form-control" value="<?php echo e(old('proposal_title', $application->proposal_title ?? '')); ?>" placeholder="Enter your research proposal title">
                                                            <span class="text-danger">
                                                            <?php $__errorArgs = ['proposal_title'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                    <!-- Research Proposal Title -->
                                    <div class="section-block mt-4" id="publication-section">
                                        <h5 class="section-title"><b>Publication Information</b></h5>
                                        <div class="employment-records">
                                            <!-- Single Employment Record -->
                                            <div class="employment-record">
                                                <div class="row">
                                                    <div class="col-md-12">
                                                        <div class="form-group">
                                                            <label>Summary of the research and publication detials </label>
                                                            <textarea class="form-control ckeditor" name="research_summary" rows="20" placeholder="Enter summary of the research and publication detials"><?php echo e(old('research_summary', $application->research_summary ?? '')); ?></textarea>
                                                            <span class="text-danger">
                                                            <?php $__errorArgs = ['research_summary'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>


                                    <!-- Supervisors Section -->
                                    <div class="section-block mt-4" id="supervisor-section">
                                        <h5 class="section-title"><b>Supervisor Information</b></h5>

                                        <!-- First Supervisor -->
                                        <div class="supervisor-block mb-4">
                                            <h6>First Supervisor</h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Full Name <span class="required">*</span></label>
                                                        <input type="text" name="supervisor1_name"
                                                            class="form-control" value="<?php echo e(old('supervisor1_name', $firstSupervisor->full_name ?? '')); ?>">
                                                            <span class="text-danger">
                                                            <?php $__errorArgs = ['supervisor1_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Email <span class="required">*</span></label>
                                                        <input type="email" name="supervisor1_email"
                                                            class="form-control" value="<?php echo e(old('supervisor1_email', $firstSupervisor->email ?? '')); ?>">
                                                            <span class="text-danger">
                                                            <?php $__errorArgs = ['supervisor1_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Address <span class="required">*</span></label>
                                                        <textarea name="supervisor1_address" class="form-control"><?php echo e(old('supervisor1_address', $firstSupervisor->address ?? '')); ?></textarea>
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['supervisor1_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Mobile Number <span class="required">*</span></label>
                                                        <input type="text" name="supervisor1_mobile"
                                                            class="form-control" value="<?php echo e(old('supervisor1_mobile', $firstSupervisor->mobile_no ?? '')); ?>">
                                                            <span class="text-danger">
                                                            <?php $__errorArgs = ['supervisor1_mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Home Telephone Number </label>
                                                        <input type="text" name="supervisor1_tel_home"
                                                            class="form-control" value="<?php echo e(old('supervisor1_tel_home', $firstSupervisor->tel_home ?? '')); ?>">
                                                            <span class="text-danger">
                                                            <?php $__errorArgs = ['supervisor1_tel_home'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Office Telephone Number </label>
                                                        <input type="text" name="supervisor1_tel_office"
                                                            class="form-control" value="<?php echo e(old('supervisor1_tel_office', $firstSupervisor->tel_office ?? '')); ?>">
                                                            <span class="text-danger">
                                                            <?php $__errorArgs = ['supervisor1_tel_office'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>

                                        <!-- Second Supervisor -->
                                        <div class="supervisor-block">
                                            <h6>Second Supervisor</h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Full Name </label>
                                                        <input type="text" name="supervisor2_name"
                                                            class="form-control" value="<?php echo e(old('supervisor2_name', $secondSupervisor->full_name ?? '')); ?>">
                                                            <span class="text-danger">
                                                            <?php $__errorArgs = ['supervisor2_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Email </label>
                                                        <input type="email" name="supervisor2_email"
                                                            class="form-control" value="<?php echo e(old('supervisor2_email', $secondSupervisor->email ?? '')); ?>">
                                                            <span class="text-danger">
                                                            <?php $__errorArgs = ['supervisor2_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Address </label>
                                                        <textarea name="supervisor2_address" class="form-control"><?php echo e(old('supervisor2_address', $secondSupervisor->address ?? '')); ?></textarea>
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['supervisor2_address'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Mobile Number </label>
                                                        <input type="text" name="supervisor2_mobile"
                                                            class="form-control" value="<?php echo e(old('supervisor2_mobile', $secondSupervisor->mobile_no ?? '')); ?>">
                                                            <span class="text-danger">
                                                            <?php $__errorArgs = ['supervisor2_mobile'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>

                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Home Telephone Number </label>
                                                        <input type="text" name="supervisor2_tel_home"
                                                            class="form-control" value="<?php echo e(old('supervisor2_tel_home', $secondSupervisor->tel_home ?? '')); ?>">
                                                            <span class="text-danger">
                                                            <?php $__errorArgs = ['supervisor2_tel_home'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Office Telephone Number </label>
                                                        <input type="text" name="supervisor2_tel_office"
                                                            class="form-control" value="<?php echo e(old('supervisor2_tel_office', $secondSupervisor->tel_office ?? '')); ?>">
                                                            <span class="text-danger">
                                                            <?php $__errorArgs = ['supervisor2_tel_office'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    </div>
                                    <?php endif; ?>

                                    <!-- Referee Section -->
                                    <div class="section-block mt-4" id="referee-section">
                                        <h5 class="section-title"><b>Referee Information</b></h5>

                                        <!-- First Referee -->
                                        <div class="referee-block mb-4">
                                            <h6>First Referee</h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Full Name <span class="required">*</span></label>
                                                        <input type="text" name="referee1_name" class="form-control" value="<?php echo e(old('referee1_name', $application->ref_name1 ?? '')); ?>">
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['referee1_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Email <span class="required">*</span></label>
                                                        <input type="email" name="referee1_email" class="form-control" value="<?php echo e(old('referee1_email', $application->ref_email1 ?? '')); ?>">
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['referee1_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Contact Number <span class="required">*</span></label>
                                                        <input type="text" name="referee1_phone" class="form-control" value="<?php echo e(old('referee1_phone', $application->ref_mobile1 ?? '')); ?>">
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['referee1_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Second Referee -->
                                        <div class="referee-block">
                                            <h6>Second Referee</h6>
                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Full Name <span class="required">*</span></label>
                                                        <input type="text" name="referee2_name" class="form-control" value="<?php echo e(old('referee2_name', $application->ref_name2 ?? '')); ?>">
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['referee2_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Email <span class="required">*</span></label>
                                                        <input type="email" name="referee2_email" class="form-control" value="<?php echo e(old('referee2_email', $application->ref_email2 ?? '')); ?>">
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['referee2_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="row">
                                                <div class="col-md-6">
                                                    <div class="form-group">
                                                        <label>Contact Number <span class="required">*</span></label>
                                                        <input type="text" name="referee2_phone" class="form-control" value="<?php echo e(old('referee2_phone', $application->ref_mobile2 ?? '')); ?>">
                                                        <span class="text-danger">
                                                            <?php $__errorArgs = ['referee2_phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                            <?php echo e($message); ?>

                                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                           </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Document Upload Section -->
                                    <div class="section-block mt-4" id="required-document-section">
                                        <h5 class="section-title"><b>Required Documents</b></h5>

                                        <!-- Photo Upload -->
                                        <div class="document-upload-block mb-4">
                                            <h6>Photo</h6>
                                            <div class="form-group">
                                                <label>Upload Photo (JPG/PNG)
                                                    <?php if(!isset($existingDocuments[1])): ?>
                                                        <span class="required">*</span>
                                                    <?php endif; ?>
                                                </label>

                                                <?php if(isset($existingDocuments[1])): ?>
                                                    <div class="existing-file-info mb-2 p-2 bg-light border rounded">
                                                        <div class="d-flex align-items-center">
                                                            <div class="file-preview mr-3">
                                                                <?php
                                                                    $extension = strtolower(pathinfo($existingDocuments[1]->original_file_name, PATHINFO_EXTENSION));
                                                                ?>
                                                                <?php if(in_array($extension, ['jpg', 'jpeg', 'png', 'gif'])): ?>
                                                                    <img src="<?php echo e($existingDocuments[1]->getSecureViewUrl()); ?>"
                                                                         alt="Preview"
                                                                         class="img-thumbnail"
                                                                         style="width: 60px; height: 60px; object-fit: cover;">
                                                                <?php else: ?>
                                                                    <div class="pdf-preview d-flex align-items-center justify-content-center bg-danger text-white"
                                                                         style="width: 60px; height: 60px; border-radius: 4px;">
                                                                        <i class="fas fa-file-pdf" style="font-size: 24px;"></i>
                                                                    </div>
                                                                <?php endif; ?>
                                                            </div>
                                                            <div class="flex-grow-1">
                                                                <div>
                                                                    <i class="fas fa-file-image text-success"></i>
                                                                    <strong>Current file:</strong> <?php echo e($existingDocuments[1]->original_file_name); ?>

                                                                </div>
                                                                <small class="text-muted">Upload a new file to replace the current one</small>
                                                            </div>
                                                            <div class="ml-2">
                                                                <a href="<?php echo e($existingDocuments[1]->getSecureViewUrl()); ?>" target="_blank" class="btn btn-sm btn-outline-warning">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>

                                                <input type="file" name="profile_photo" class="form-control"
                                                    accept=".jpg,.jpeg,.png">
                                                <small class="form-text text-muted">Maximum file size: 2MB</small>
                                                <span class="text-danger">
                                                    <?php $__errorArgs = ['profile_photo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <?php echo e($message); ?>

                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Educational Certificates -->
                                        <div class="document-upload-block mb-4">
                                            <h6>First Degree Certificates</h6>
                                            <div class="form-group">
                                                <label>Degree Certificates (PDF)
                                                    <?php if(!isset($existingDocuments[2])): ?>
                                                        <span class="required">*</span>
                                                    <?php endif; ?>
                                                </label>

                                                <?php if(isset($existingDocuments[2])): ?>
                                                    <div class="existing-file-info mb-2 p-2 bg-light border rounded">
                                                        <div class="d-flex align-items-center">
                                                            <div class="file-preview mr-3">
                                                                <div class="pdf-preview d-flex align-items-center justify-content-center bg-danger text-white"
                                                                     style="width: 60px; height: 60px; border-radius: 4px;">
                                                                    <i class="fas fa-file-pdf" style="font-size: 24px;"></i>
                                                                </div>
                                                            </div>
                                                            <div class="flex-grow-1">
                                                                <div>
                                                                    <i class="fas fa-file-pdf text-danger"></i>
                                                                    <strong>Current file:</strong> <?php echo e($existingDocuments[2]->original_file_name); ?>

                                                                </div>
                                                                <small class="text-muted">Upload a new file to replace the current one</small>
                                                            </div>
                                                            <div class="ml-2">
                                                                <a href="<?php echo e($existingDocuments[2]->getSecureViewUrl()); ?>" target="_blank" class="btn btn-sm btn-outline-warning">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>

                                                <input type="file" name="degree_certificate" class="form-control"
                                                    accept=".pdf">
                                                <small class="form-text text-muted">Upload relevant degree certificates in
                                                    PDF format</small>
                                                <span class="text-danger">
                                                    <?php $__errorArgs = ['degree_certificate'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <?php echo e($message); ?>

                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </span>
                                            </div>
                                            <div class="form-group">
                                                <label>Degree Trascript (PDF)
                                                    <?php if(!isset($existingDocuments[3])): ?>
                                                        <span class="required">*</span>
                                                    <?php endif; ?>
                                                </label>

                                                <?php if(isset($existingDocuments[3])): ?>
                                                    <div class="existing-file-info mb-2 p-2 bg-light border rounded">
                                                        <div class="d-flex align-items-center">
                                                            <div class="file-preview mr-3">
                                                                <div class="pdf-preview d-flex align-items-center justify-content-center bg-danger text-white"
                                                                     style="width: 60px; height: 60px; border-radius: 4px;">
                                                                    <i class="fas fa-file-pdf" style="font-size: 24px;"></i>
                                                                </div>
                                                            </div>
                                                            <div class="flex-grow-1">
                                                                <div>
                                                                    <i class="fas fa-file-pdf text-danger"></i>
                                                                    <strong>Current file:</strong> <?php echo e($existingDocuments[3]->original_file_name); ?>

                                                                </div>
                                                                <small class="text-muted">Upload a new file to replace the current one</small>
                                                            </div>
                                                            <div class="ml-2">
                                                                <a href="<?php echo e($existingDocuments[3]->getSecureViewUrl()); ?>" target="_blank" class="btn btn-sm btn-outline-warning">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>

                                                <input type="file" name="degree_transcript" class="form-control"
                                                    accept=".pdf">
                                                <small class="form-text text-muted">Upload relevant degree transcript in
                                                    PDF format</small>
                                                <span class="text-danger">
                                                    <?php $__errorArgs = ['degree_transcript'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <?php echo e($message); ?>

                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Employment Records -->
                                        <div class="document-upload-block mb-4">
                                            <h6>Birth Certificates</h6>
                                            <div class="form-group">
                                                <label>Birth Certificates (PDF)
                                                    <?php if(!isset($existingDocuments[4])): ?>
                                                        <span class="required">*</span>
                                                    <?php endif; ?>
                                                </label>

                                                <?php if(isset($existingDocuments[4])): ?>
                                                    <div class="existing-file-info mb-2 p-2 bg-light border rounded">
                                                        <div class="d-flex align-items-center">
                                                            <div class="file-preview mr-3">
                                                                <div class="pdf-preview d-flex align-items-center justify-content-center bg-danger text-white"
                                                                     style="width: 60px; height: 60px; border-radius: 4px;">
                                                                    <i class="fas fa-file-pdf" style="font-size: 24px;"></i>
                                                                </div>
                                                            </div>
                                                            <div class="flex-grow-1">
                                                                <div>
                                                                    <i class="fas fa-file-pdf text-danger"></i>
                                                                    <strong>Current file:</strong> <?php echo e($existingDocuments[4]->original_file_name); ?>

                                                                </div>
                                                                <small class="text-muted">Upload a new file to replace the current one</small>
                                                            </div>
                                                            <div class="ml-2">
                                                                <a href="<?php echo e($existingDocuments[4]->getSecureViewUrl()); ?>" target="_blank" class="btn btn-sm btn-outline-warning">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>

                                                <input type="file" name="birth_certificate" class="form-control"
                                                    accept=".pdf">
                                                <small class="form-text text-muted">Upload birth certificate in PDF
                                                    format</small>
                                                <span class="text-danger">
                                                    <?php $__errorArgs = ['birth_certificate'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <?php echo e($message); ?>

                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </span>
                                            </div>
                                        </div>

                                        <!-- Consent Letter -->
                                        <div class="document-upload-block">
                                            <h6>NIC/Passport</h6>
                                            <div class="form-group">
                                                <label>NIC/Passport (PDF)
                                                    <?php if(!isset($existingDocuments[5])): ?>
                                                        <span class="required">*</span>
                                                    <?php endif; ?>
                                                </label>

                                                <?php if(isset($existingDocuments[5])): ?>
                                                    <div class="existing-file-info mb-2 p-2 bg-light border rounded">
                                                        <div class="d-flex align-items-center">
                                                            <div class="file-preview mr-3">
                                                                <div class="pdf-preview d-flex align-items-center justify-content-center bg-danger text-white"
                                                                     style="width: 60px; height: 60px; border-radius: 4px;">
                                                                    <i class="fas fa-file-pdf" style="font-size: 24px;"></i>
                                                                </div>
                                                            </div>
                                                            <div class="flex-grow-1">
                                                                <div>
                                                                    <i class="fas fa-file-pdf text-danger"></i>
                                                                    <strong>Current file:</strong> <?php echo e($existingDocuments[5]->original_file_name); ?>

                                                                </div>
                                                                <small class="text-muted">Upload a new file to replace the current one</small>
                                                            </div>
                                                            <div class="ml-2">
                                                                <a href="<?php echo e($existingDocuments[5]->getSecureViewUrl()); ?>" target="_blank" class="btn btn-sm btn-outline-warning">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>

                                                <input type="file" name="nic_passport_doc" class="form-control"
                                                    accept=".pdf">
                                                <small class="form-text text-muted">Upload NIC/Passport in PDF
                                                    format</small>
                                                <span class="text-danger">
                                                    <?php $__errorArgs = ['nic_passport_doc'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <?php echo e($message); ?>

                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </span>
                                            </div>
                                        </div>
                                        <?php if($courseData->course_main_category == 42): ?>
                                        <!-- Consent Letter -->
                                        <div class="document-upload-block">
                                            <h6>Research Proposal/ Concept Paper</h6>
                                            <div class="form-group">
                                                <label>Research Proposal/ Concept Paper (PDF)
                                                    <?php if(!isset($existingDocuments[6])): ?>
                                                        <span class="required">*</span>
                                                    <?php endif; ?>
                                                </label>

                                                <?php if(isset($existingDocuments[6])): ?>
                                                    <div class="existing-file-info mb-2 p-2 bg-light border rounded">
                                                        <div class="d-flex align-items-center">
                                                            <div class="file-preview mr-3">
                                                                <div class="pdf-preview d-flex align-items-center justify-content-center bg-danger text-white"
                                                                     style="width: 60px; height: 60px; border-radius: 4px;">
                                                                    <i class="fas fa-file-pdf" style="font-size: 24px;"></i>
                                                                </div>
                                                            </div>
                                                            <div class="flex-grow-1">
                                                                <div>
                                                                    <i class="fas fa-file-pdf text-danger"></i>
                                                                    <strong>Current file:</strong> <?php echo e($existingDocuments[6]->original_file_name); ?>

                                                                </div>
                                                                <small class="text-muted">Upload a new file to replace the current one</small>
                                                            </div>
                                                            <div class="ml-2">
                                                                <a href="<?php echo e($existingDocuments[6]->getSecureViewUrl()); ?>" target="_blank" class="btn btn-sm btn-outline-warning">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>

                                                <input type="file" name="research_proposal1" class="form-control"
                                                    accept=".pdf">
                                                <small class="form-text text-muted">Upload Research Proposal/ Concept Paper
                                                    in PDF format</small>
                                                <span class="text-danger">
                                                    <?php $__errorArgs = ['research_proposal1'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <?php echo e($message); ?>

                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </span>
                                            </div>

                                            <div class="form-group">
                                                <label>Additional Research Proposal/ Concept Paper (PDF) <span class="text-muted">(Optional)</span></label>

                                                <?php if(isset($existingDocuments[7])): ?>
                                                    <div class="existing-file-info mb-2 p-2 bg-light border rounded">
                                                        <div class="d-flex align-items-center">
                                                            <div class="file-preview mr-3">
                                                                <div class="pdf-preview d-flex align-items-center justify-content-center bg-danger text-white"
                                                                     style="width: 60px; height: 60px; border-radius: 4px;">
                                                                    <i class="fas fa-file-pdf" style="font-size: 24px;"></i>
                                                                </div>
                                                            </div>
                                                            <div class="flex-grow-1">
                                                                <div>
                                                                    <i class="fas fa-file-pdf text-danger"></i>
                                                                    <strong>Current file:</strong> <?php echo e($existingDocuments[7]->original_file_name); ?>

                                                                </div>
                                                                <small class="text-muted">Upload a new file to replace the current one</small>
                                                            </div>
                                                            <div class="ml-2">
                                                                <a href="<?php echo e($existingDocuments[7]->getSecureViewUrl()); ?>" target="_blank" class="btn btn-sm btn-outline-warning">
                                                                    <i class="fas fa-eye"></i> View
                                                                </a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                <?php endif; ?>

                                                <input type="file" name="research_proposal2" class="form-control"
                                                    accept=".pdf">
                                                <small class="form-text text-muted">Upload Research Proposal/ Concept Paper
                                                    in PDF format</small>
                                                <span class="text-danger">
                                                    <?php $__errorArgs = ['research_proposal2'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                        <?php echo e($message); ?>

                                                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                                </span>
                                            </div>
                                        </div>
                                        <?php endif; ?>
                                    </div>


                                    <!-- Terms and Conditions Checkbox -->
                                    <div class="card card-outline card-danger mt-3">
                                        <div class="card-body">
                                            <div class="icheck-danger">
                                                <input type="checkbox" id="agreeTerms" name="terms" value="1" <?php echo e(old('terms') ? 'checked' : ''); ?>>
                                                <label for="agreeTerms">
                                                    I hereby declare that the particulars furnished by me in the application
                                                    are true and accurate. I am aware that if any particulars contained
                                                    herein are found to be false or incorrect, I am liable to
                                                    disqualification if the inaccuracy is discovered before the selection
                                                    and dismissal without any compensation if the inaccuracy is discovered
                                                    after the selection.
                                                </label>

                                            </div>
                                            <span class="text-danger">
                                                <?php $__errorArgs = ['terms'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                                    <?php echo e($message); ?>

                                                    <br>
                                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                            </span>
                                        </div>
                                    </div>
                                    <!-- Submit Button -->
                                    <div class="form-actions mt-3 text-center">
                                        <input type="submit" name="save" class="btn btn-warning" value="Save">
                                        <input type="submit" name="submit" class="btn btn-success"
                                            value="Submit & Pay">
                                    </div>
                </form>
            </div>
    </div>
    </div>
    </div>
    </section>
    </div>
    <style>
        .existing-file-info {
            background-color: #f8f9fa !important;
            border: 1px solid #e9ecef !important;
            border-radius: 0.375rem !important;
            padding: 0.75rem !important;
            margin-bottom: 0.5rem !important;
        }

        .existing-file-info i {
            margin-right: 0.5rem;
        }

        .existing-file-info .btn {
            font-size: 0.875rem;
            padding: 0.25rem 0.5rem;
        }

        .existing-file-info small {
            font-size: 0.8rem;
            margin-top: 0.25rem;
        }

        .file-preview {
            flex-shrink: 0;
        }

        .file-preview img {
            border: 2px solid #dee2e6;
            border-radius: 6px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .pdf-preview {
            border: 2px solid #dc3545;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: all 0.2s ease;
        }

        .pdf-preview:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
        }

        .existing-file-info .d-flex {
            align-items: flex-start;
        }

        .existing-file-info .flex-grow-1 {
            min-width: 0; /* Allows text to wrap properly */
        }
    </style>

    <script>
        $(document).ready(function() {
            // Get all section navigation items
            const sectionNavItems = $('.section-nav-item');

            // Add click event listener to each item
            sectionNavItems.on('click', function() {
                // Get the target section ID from the data-section attribute
                const targetSectionId = $(this).attr('data-section');
                const targetSection = $('#' + targetSectionId);

                if (targetSection.length) {
                    // Scroll to the target section smoothly
                    $('html, body').animate({
                        scrollTop: targetSection.offset().top - 100
                    }, 500);

                    // Optionally, add active class to the clicked item
                    sectionNavItems.removeClass('active');
                    $(this).addClass('active');
                }

                // Auto-collapse mobile navigation after selection
                if (window.innerWidth < 768) {
                    $('#sectionNavCollapse').collapse('hide');
                }
            });

            // Qualifications functionality
            let qualificationCounter = 0;
            const qualificationsContainer = $('#qualifications-container');
            const addQualificationBtn = $('#add-qualification');
            const qualificationsTable = $('#qualifications-table');
            const qualificationsTableBody = qualificationsTable.find('tbody');

            // Initialize DataTable reference (it's already initialized in frontend_master.blade.php)
            let qualificationsDataTable = null;

            // Wait for DataTable to be initialized
            setTimeout(function() {
                if ($.fn.DataTable.isDataTable('#qualifications-table')) {
                    qualificationsDataTable = $('#qualifications-table').DataTable();
                }
            }, 100);

            // Add qualification button click handler
            addQualificationBtn.on('click', function(e) {
                e.preventDefault();

                // Get field values
                var qualificationType = $('#qualification_type_id');
                var qualificationName = $('#qulification_name');
                var university = $('#qualification_university');
                var effectiveDate = $('#qualification_effective_date');
                var duration = $('#qualification_duration');
                var majorSubject = $('#qualification_major_subject');

                // Validate one by one
                if (!qualificationType.val()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Required',
                        text: 'Please select a qualification type.'
                    }).then(() => {
                        qualificationType.focus();
                    });
                    return;
                }
                if (!qualificationName.val()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Required',
                        text: 'Please enter the qualification name.'
                    }).then(() => {
                        qualificationName.focus();
                    });
                    return;
                }
                if (!university.val()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Required',
                        text: 'Please enter the university/institution'
                    }).then(() => {
                        university.focus();
                    });
                    return;
                }
                if (!effectiveDate.val()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Required',
                        text: 'Please enter the qualification effective date.'
                    }).then(() => {
                        effectiveDate.focus();
                    });
                    return;
                }
                if (!duration.val()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Required',
                        text: 'Please enter the duration of qualification.'
                    }).then(() => {
                        duration.focus();
                    });
                    return;
                }

                // If all fields are valid, proceed to add qualification
                addQualificationToTable();
            });

            // Function to add qualification to table
            function addQualificationToTable() {


                const qualificationBlock = qualificationsContainer.find('.qualification-block');

                // Get form values
                const qualificationType = qualificationBlock.find('select[name="qualification_type_id"]');
                const qualificationName = qualificationBlock.find('input[name="qulification_name"]');
                const university = qualificationBlock.find('input[name="qualification_university"]');
                const effectiveDate = qualificationBlock.find('input[name="qualification_effective_date"]');
                const duration = qualificationBlock.find('input[name="qualification_duration"]');
                const majorSubject = qualificationBlock.find('input[name="qualification_major_subject"]');

                // Create table row data for DataTable
                const rowData = [
                    qualificationType.find('option:selected').text(),
                    qualificationName.val(),
                    university.val(),
                    effectiveDate.val(),
                    duration.val() + ' years',
                    majorSubject.val() || '-',
                    `<button type="button" class="btn btn-sm btn-danger remove-qualification" data-counter="${qualificationCounter}">Delete</button>`
                ];

                // Add hidden inputs to store qualification data
                const hiddenInputs = `
                    <input type="hidden" name="qualifications[${qualificationCounter}][qualification_type_id]" value="${qualificationType.val()}">
                    <input type="hidden" name="qualifications[${qualificationCounter}][qulification_name]" value="${qualificationName.val()}">
                    <input type="hidden" name="qualifications[${qualificationCounter}][qualification_university]" value="${university.val()}">
                    <input type="hidden" name="qualifications[${qualificationCounter}][qualification_effective_date]" value="${effectiveDate.val()}">
                    <input type="hidden" name="qualifications[${qualificationCounter}][qualification_duration]" value="${duration.val()}">
                    <input type="hidden" name="qualifications[${qualificationCounter}][qualification_major_subject]" value="${majorSubject.val()}">
                `;

                // Add hidden inputs to the form
                const form = $('#applicationForm');
                const hiddenInputsContainer = $('<div>').html(hiddenInputs);
                form.append(hiddenInputsContainer);

                // Add row to DataTable
                if (qualificationsDataTable) {
                    qualificationsDataTable.row.add(rowData).draw();
                } else {
                    const row = $('<tr>').html(`
                        <td>${qualificationType.find('option:selected').text()}</td>
                        <td>${qualificationName.val()}</td>
                        <td>${university.val()}</td>
                        <td>${effectiveDate.val()}</td>
                        <td>${duration.val()} years</td>
                        <td>${majorSubject.val() || '-'}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger remove-qualification" data-counter="${qualificationCounter}">Delete</button>
                        </td>
                    `);
                    qualificationsTableBody.append(row);
                }

                qualificationCounter++;

                // Clear form fields
                qualificationType.val('');
                qualificationName.val('');
                university.val('');
                effectiveDate.val('');
                duration.val('');
                majorSubject.val('');

                // Show success message
                showNotification('Qualification added successfully!', 'success');
            }

            // Remove qualification from table
            $(document).on('click', '.remove-qualification', function(e) {
                const button = $(this);
                const counter = button.data('counter');

                if (qualificationsDataTable) {
                    // For DataTable
                    const row = qualificationsDataTable.row(button.closest('tr'));
                    row.remove().draw();
                } else {
                    // For regular table
                    button.closest('tr').remove();
                }

                // Remove hidden inputs for this qualification
                const form = $('#applicationForm');
                const hiddenInputs = form.find(`input[name^="qualifications[${counter}]"]`);
                hiddenInputs.remove();

                // Reindex remaining qualifications
                reindexQualifications();

                showNotification('Qualification removed successfully!', 'success');
            });

            // Function to reindex qualifications after removal
            function reindexQualifications() {
                const form = $('#applicationForm');
                const hiddenInputs = form.find('input[name^="qualifications["]');
                let newIndex = 0;

                hiddenInputs.each(function() {
                    const name = $(this).attr('name');
                    const newName = name.replace(/qualifications\[\d+\]/, `qualifications[${newIndex}]`);
                    $(this).attr('name', newName);

                    // Increment index for each group of 6 inputs (6 fields per qualification)
                    if (name.includes('major_subject')) {
                        newIndex++;
                    }
                });

                qualificationCounter = newIndex;
            }

            // Function to show notifications
            function showNotification(message, type) {
                const notification = $(`
                    <div class="alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                `);

                // Insert notification at the top of the form
                const form = $('#applicationForm');
                form.prepend(notification);

                // Auto-remove notification after 3 seconds
                setTimeout(() => {
                    notification.fadeOut(function() {
                        $(this).remove();
                    });
                }, 3000);
            }

            // Employment Details functionality
            let employmentCounter = 0;
            const employmentContainer = $('.employment-records');
            const addEmploymentBtn = $('.add-employment-record');
            const employmentTable = $('#employee-record-table');
            const employmentTableBody = employmentTable.find('tbody');

            // Initialize DataTable reference (it's already initialized in frontend_master.blade.php)
            let employmentDataTable = null;

            // Wait for DataTable to be initialized
            setTimeout(function() {
                if ($.fn.DataTable.isDataTable('#employee-record-table')) {
                    employmentDataTable = $('#employee-record-table').DataTable();
                }
            }, 100);

            // Add employment record button click handler
            addEmploymentBtn.on('click', function(e) {
                e.preventDefault();

                // Get field values from the employment form
                var workingStatus = $('.employment-record select[name="working_status"]');
                var institution = $('.employment-record input[name="institution"]');
                var empAddress = $('.employment-record input[name="emp_address"]');
                var position = $('.employment-record input[name="position"]');
                var period = $('.employment-record input[name="period"]');
                var natureOfDuty = $('.employment-record input[name="nature_of_duty"]');

                // Validate one by one
                if (!workingStatus.val()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Required',
                        text: 'Please select working status of employement record.'
                    }).then(() => {
                        workingStatus.focus();
                    });
                    return;
                }
                if (!institution.val()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Required',
                        text: 'Please enter the company you worked for.'
                    }).then(() => {
                        institution.focus();
                    });
                    return;
                }
                if (!empAddress.val()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Required',
                        text: 'Please enter the company address.'
                    }).then(() => {
                        empAddress.focus();
                    });
                    return;
                }
                if (!position.val()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Required',
                        text: 'Please enter your position/rank.'
                    }).then(() => {
                        position.focus();
                    });
                    return;
                }
                if (!period.val()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Required',
                        text: 'Please enter the employment period.'
                    }).then(() => {
                        period.focus();
                    });
                    return;
                }
                if (!natureOfDuty.val()) {
                    Swal.fire({
                        icon: 'error',
                        title: 'Required',
                        text: 'Please enter the nature of duty.'
                    }).then(() => {
                        natureOfDuty.focus();
                    });
                    return;
                }

                // If all fields are valid, proceed to add employment record
                addEmploymentToTable();
            });

            // Function to add employment record to table
            function addEmploymentToTable() {
                const employmentRecord = employmentContainer.find('.employment-record');

                // Get form values
                const workingStatus = employmentRecord.find('select[name="working_status"]');
                const institution = employmentRecord.find('input[name="institution"]');
                const empAddress = employmentRecord.find('input[name="emp_address"]');
                const position = employmentRecord.find('input[name="position"]');
                const period = employmentRecord.find('input[name="period"]');
                const natureOfDuty = employmentRecord.find('input[name="nature_of_duty"]');

                // Get working status text
                const workingStatusText = workingStatus.find('option:selected').text();

                // Create table row data for DataTable
                const rowData = [
                    institution.val(),
                    empAddress.val(),
                    position.val(),
                    period.val(),
                    natureOfDuty.val(),
                    workingStatusText,
                    `<button type="button" class="btn btn-sm btn-danger remove-employment" data-counter="${employmentCounter}">Delete</button>`
                ];

                // Add hidden inputs to store employment data
                const hiddenInputs = `
                    <input type="hidden" name="employment_records[${employmentCounter}][working_status]" value="${workingStatus.val()}">
                    <input type="hidden" name="employment_records[${employmentCounter}][institution]" value="${institution.val()}">
                    <input type="hidden" name="employment_records[${employmentCounter}][emp_address]" value="${empAddress.val()}">
                    <input type="hidden" name="employment_records[${employmentCounter}][position]" value="${position.val()}">
                    <input type="hidden" name="employment_records[${employmentCounter}][period]" value="${period.val()}">
                    <input type="hidden" name="employment_records[${employmentCounter}][nature_of_duty]" value="${natureOfDuty.val()}">
                `;

                // Add hidden inputs to the form
                const form = $('#applicationForm');
                const hiddenInputsContainer = $('<div>').html(hiddenInputs);
                form.append(hiddenInputsContainer);

                // Add row to DataTable
                if (employmentDataTable) {
                    employmentDataTable.row.add(rowData).draw();
                } else {
                    const row = $('<tr>').html(`
                        <td>${institution.val()}</td>
                        <td>${empAddress.val()}</td>
                        <td>${position.val()}</td>
                        <td>${period.val()}</td>
                        <td>${natureOfDuty.val()}</td>
                        <td>${workingStatusText}</td>
                        <td>
                            <button type="button" class="btn btn-sm btn-danger remove-employment" data-counter="${employmentCounter}">Delete</button>
                        </td>
                    `);
                    employmentTableBody.append(row);
                }

                employmentCounter++;

                // Clear form fields
                workingStatus.val('');
                institution.val('');
                empAddress.val('');
                position.val('');
                period.val('');
                natureOfDuty.val('');

                // Show success message
                showNotification('Employment record added successfully!', 'success');
            }

            // Remove employment record from table
            $(document).on('click', '.remove-employment', function(e) {
                const button = $(this);
                const counter = button.data('counter');

                if (employmentDataTable) {
                    // For DataTable
                    const row = employmentDataTable.row(button.closest('tr'));
                    row.remove().draw();
                } else {
                    // For regular table
                    button.closest('tr').remove();
                }

                // Remove hidden inputs for this employment record
                const form = $('#applicationForm');
                const hiddenInputs = form.find(`input[name^="employment_records[${counter}]"]`);
                hiddenInputs.remove();

                // Reindex remaining employment records
                reindexEmploymentRecords();

                showNotification('Employment record removed successfully!', 'success');
            });

            // Function to reindex employment records after removal
            function reindexEmploymentRecords() {
                const form = $('#applicationForm');
                const hiddenInputs = form.find('input[name^="employment_records["]');
                let newIndex = 0;

                hiddenInputs.each(function() {
                    const name = $(this).attr('name');
                    const newName = name.replace(/employment_records\[\d+\]/, `employment_records[${newIndex}]`);
                    $(this).attr('name', newName);

                    // Increment index for each group of 6 inputs (6 fields per employment record)
                    if (name.includes('nature_of_duty')) {
                        newIndex++;
                    }
                });

                employmentCounter = newIndex;
                console.log('Employment records reindexed. New counter:', employmentCounter);
            }


        });

        // Terms and conditions checkbox handling
        $('#agreeTerms').on('change', function() {
            const isChecked = $(this).is(':checked');
            const termsCard = $(this).closest('.card');

            if (isChecked) {
                // Remove error styling and add success styling
                termsCard.removeClass('terms-error border-danger').addClass('border-success');
                $(this).removeClass('is-invalid');

                // Show success feedback
                showNotification('Terms and conditions accepted!', 'success');
            } else {
                // Remove success styling
                termsCard.removeClass('border-success');
            }
        });

        // Track which button was clicked
        let clickedButtonName = '';

        // Save button click handler
        $('input[name="save"]').on('click', function() {
            clickedButtonName = 'save';
            console.log('Save button clicked');
        });

        // Submit button click handler
        $('input[name="submit"]').on('click', function() {
            clickedButtonName = 'submit';
            console.log('Submit button clicked');
        });

        // Form submission validation
        $('#applicationForm').on('submit', function(e) {
            // Check if this is a submit action (not save) using tracked button
            const isSubmit = clickedButtonName === 'submit';

            console.log('Form submitted. Clicked button:', clickedButtonName, 'Is Submit:', isSubmit);

            if (isSubmit) {
                // First, check all other validation errors before terms
                let hasOtherErrors = false;
                let errorMessage = '';

                // Check if qualifications table has any rows
                const qualificationsRows = $('#qualifications-table tbody tr').length;
                if (qualificationsRows === 0) {
                    hasOtherErrors = true;
                    errorMessage = 'Please add at least one qualification before submitting the application.';
                    e.preventDefault();
                    Swal.fire({
                        icon: 'warning',
                        title: 'Missing Qualifications',
                        text: errorMessage,
                        confirmButtonText: 'OK'
                    });
                    return false;
                }

                // Check if employment records table has any rows
                const employmentRows = $('#employee-record-table tbody tr').length;
                if (employmentRows === 0) {
                    hasOtherErrors = true;
                    errorMessage = 'Please add at least one employment record before submitting the application.';
                    e.preventDefault();
                    Swal.fire({
                        icon: 'warning',
                        title: 'Missing Employment Records',
                        text: errorMessage,
                        confirmButtonText: 'OK'
                    });
                    return false;
                }

            }

            return true;
        });

        // Focus on first error field when page loads with validation errors
        <?php if($errors->any()): ?>
            $(document).ready(function() {
                // Highlight all error fields
                var errorFields = [];
                <?php $__currentLoopData = $errors->keys(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $fieldName): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    var field = $('[name="<?php echo e($fieldName); ?>"]').first();
                    if (field.length > 0) {
                        field.addClass('is-invalid');
                        errorFields.push(field);
                    }
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                // Focus on the first error field
                if (errorFields.length > 0) {
                    var firstErrorField = errorFields[0];

                    // Scroll to the field with some offset
                    $('html, body').animate({
                        scrollTop: firstErrorField.offset().top - 100
                    }, 800, function() {
                        // Focus on the field after scrolling
                        firstErrorField.focus();

                        // Add a special highlight effect to the first field
                        firstErrorField.addClass('error-highlight');
                        setTimeout(function() {
                            firstErrorField.removeClass('error-highlight');
                        }, 3000);
                    });

                    // Show a notification about the errors
                    showNotification('<?php echo e($errors->count()); ?> validation error<?php echo e($errors->count() > 1 ? "s" : ""); ?> found. Please check the highlighted fields.', 'error');
                }

                // Remove error styling when user starts typing
                $('.is-invalid').on('input change', function() {
                    $(this).removeClass('is-invalid');
                });
            });
        <?php endif; ?>
    </script>



    <style>
        .application-wrapper {
            width: 100%;
            max-width: 100%;
            margin: 0;
            box-sizing: border-box;
        }

        /* Responsive container adjustments */
        @media (max-width: 767.98px) {
            .container {
                padding-left: 10px;
                padding-right: 10px;
            }
        }

        .sticky-nav-container {
            position: relative;
        }

        .sticky-nav {
            background-color: #f8f9fa;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
            padding: 20px;
        }

        /* Desktop and tablet styles */
        @media (min-width: 768px) {
            .sticky-nav {
                position: sticky;
                top: 20px;
                height: calc(100vh - 40px);
                overflow-y: auto;
            }
        }

        /* Mobile styles */
        @media (max-width: 767.98px) {
            .sticky-nav-container {
                margin-bottom: 20px;
            }

            .sticky-nav {
                position: relative;
                height: auto;
                padding: 15px;
            }

            .application-wrapper {
                padding: 10px;
            }

            .section-block {
                padding: 15px;
                margin-bottom: 15px;
            }
        }

        /* Extra small devices */
        @media (max-width: 575.98px) {
            .sticky-nav {
                padding: 10px;
            }

            .section-block {
                padding: 10px;
            }

            .card-body {
                padding: 15px;
            }
        }

        .section-title {
            border-bottom: 2px solid #f4f4f4;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }

        .required {
            color: red;
        }

        .section-block {
            background: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }

        .form-actions {
            text-align: center;
        }

        .section-nav-item.active {
            background-color: #990000;
            font-weight: bold;
        }

        /* Smooth scrolling for anchor links */
        html {
            scroll-behavior: smooth;
        }

        /* Table container - default (desktop/tablet) */
        .table-container {
            width: 100%;
            overflow: visible;
        }

        #qualifications-table,
        #employee-record-table {
            margin-top: 20px;
            width: 100%;
            table-layout: auto;
        }

        #qualifications-table th,
        #employee-record-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            text-align: center;
            white-space: nowrap;
            padding: 12px 8px;
        }

        #qualifications-table td,
        #employee-record-table td {
            text-align: center;
            padding: 12px 8px;
            word-wrap: break-word;
        }

        /* Desktop and Tablet: No horizontal scroll, full table display */
        @media (min-width: 768px) {
            .table-container {
                overflow: visible;
            }

            #qualifications-table,
            #employee-record-table {
                width: 100%;
                min-width: auto;
                table-layout: auto;
            }

            .table td, .table th {
                white-space: normal;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }
        }

        .alert {
            margin-bottom: 20px;
        }

        .alert-dismissible .close {
            padding: 0.75rem 1.25rem;
        }

        /* Validation error styling */
        .alert-danger {
            border-left: 4px solid #dc3545;
        }

        .alert-danger h6 {
            color: #721c24;
            margin-bottom: 15px;
        }

        .alert-danger ul {
            margin-bottom: 0;
        }

        .alert-danger li {
            margin-bottom: 5px;
            color: #721c24;
        }

        .alert-danger li:last-child {
            margin-bottom: 0;
        }

        .alert-danger .fas {
            margin-right: 8px;
        }

        /* Error field styling */
        .is-invalid {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }

        .is-invalid:focus {
            border-color: #dc3545 !important;
            box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25) !important;
        }

        /* Terms and conditions styling */
        .card.border-success {
            border-color: #28a745 !important;
        }

        .card.border-danger {
            border-color: #dc3545 !important;
        }

        #agreeTerms:checked + label {
            color: #28a745;
            font-weight: 500;
        }

        .terms-error {
            border: 2px solid #dc3545 !important;
            background-color: rgba(220, 53, 69, 0.05) !important;
        }

        /* Error field highlight animation for first error */
        .error-highlight {
            border: 2px solid #dc3545 !important;
            box-shadow: 0 0 15px rgba(220, 53, 69, 0.4) !important;
            background-color: rgba(220, 53, 69, 0.05) !important;
            animation: errorPulse 2s ease-in-out;
        }

        @keyframes errorPulse {
            0% {
                box-shadow: 0 0 5px rgba(220, 53, 69, 0.3);
            }
            25% {
                box-shadow: 0 0 20px rgba(220, 53, 69, 0.6);
            }
            50% {
                box-shadow: 0 0 25px rgba(220, 53, 69, 0.7);
            }
            75% {
                box-shadow: 0 0 20px rgba(220, 53, 69, 0.6);
            }
            100% {
                box-shadow: 0 0 15px rgba(220, 53, 69, 0.4);
            }
        }

        /* Validation error styling */
        .alert-danger {
            border-left: 4px solid #dc3545;
        }

        .alert-danger h6 {
            color: #721c24;
            margin-bottom: 15px;
        }

        .alert-danger ul {
            margin-bottom: 0;
        }

        .alert-danger li {
            margin-bottom: 5px;
            color: #721c24;
        }

        .alert-danger li:last-child {
            margin-bottom: 0;
        }

        .alert-danger .fas {
            margin-right: 8px;
        }

        /* Form responsive improvements */
        @media (max-width: 767.98px) {
            .form-group label {
                font-size: 14px;
                margin-bottom: 5px;
            }

            .form-control {
                font-size: 14px;
                padding: 8px 12px;
            }

            .btn {
                font-size: 14px;
                padding: 8px 16px;
            }

            /* Mobile: Enable horizontal scroll for tables */
            .table-container {
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
            }

            #qualifications-table,
            #employee-record-table {
                min-width: 800px; /* Minimum width to trigger horizontal scroll */
                font-size: 12px;
            }

            .table td, .table th {
                padding: 8px 6px;
                vertical-align: middle;
                white-space: nowrap;
                min-width: 80px;
            }
        }

        @media (max-width: 575.98px) {
            .form-group label {
                font-size: 13px;
            }

            .form-control {
                font-size: 13px;
                padding: 6px 10px;
            }

            .btn {
                font-size: 13px;
                padding: 6px 12px;
            }

            /* Extra small screens: Maintain horizontal scroll with smaller elements */
            #qualifications-table,
            #employee-record-table {
                min-width: 700px; /* Slightly smaller minimum width */
                font-size: 11px;
            }

            .table td, .table th {
                padding: 6px 4px;
                white-space: nowrap;
                min-width: 70px;
            }

            .btn-sm {
                font-size: 11px;
                padding: 4px 8px;
            }
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('frontend.frontend_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/frontend/application/index.blade.php ENDPATH**/ ?>