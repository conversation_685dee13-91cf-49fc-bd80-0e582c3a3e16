<?php $__env->startSection('admin'); ?>
<section class="content">
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">Course Application Details</h3>
                </div>
                <div class="card-body">
                    <!-- Course Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h4>Course Information</h4>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Course Code</th>
                                    <td><?php echo e($course->id ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Course Name</th>
                                    <td><?php echo e($course->course_name ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Course Category</th>
                                    <td><?php echo e($course->courseMainCategory->category_name ?? 'N/A'); ?></td>
                                </tr>

                            </table>
                        </div>
                        <div class="col-md-6">
                            <h4>Batch Details</h4>
                            <table class="table table-bordered">
                                <tr>
                                    <th>Academic Year</th>
                                    <td><?php echo e($application->reg_year ?? 'N/A'); ?></td>
                                </tr>
                                <tr>
                                    <th>Batch</th>
                                    <td><?php echo e($application->batch ?? 'N/A'); ?></td>
                                </tr>

                            </table>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            <h4>Payment Details</h4>
                            <table class="table table-bordered">
                                <thead>
                                    <tr>
                                        <th>Fee Type</th>
                                        <th>Amount</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__empty_1 = true; $__currentLoopData = $payments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td><?php echo e($payment->incomeType->income_type_name); ?></td>
                                        <td><?php echo e(number_format($payment->amount, 2)); ?></td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="4" class="text-center">No payment records found</td>
                                    </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                    <!-- Application Status -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    <h4>Application Open Active Status</h4>
                                </div>
                                <div class="card-body">
                                    <div class="form-group">
                                        <label>Current Status:</label>
                                        <span class="badge badge-<?php echo e($application->active_status == 1 ? 'success' : 'danger'); ?>">
                                            <?php echo e($application->active_status == 1 ? 'Active' : 'Inactive'); ?>

                                        </span>
                                    </div>

                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('application.initiate.active')): ?>

                                    <?php if(auth()->guard()->check()): ?>
                                        <?php if($application->active_status == 1): ?>
                                            <a href="<?php echo e(route('application.initiate.inactive', $application->id)); ?>" class="btn btn-danger">Deactivate</a>
                                        <?php else: ?>
                                            <a href="<?php echo e(route('application.initiate.active', $application->id)); ?>" class="btn btn-success">Activate</a>
                                        <?php endif; ?>

                                        <a href="<?php echo e(route('application.initiate.index')); ?>" class="btn <?php echo e(auth()->user()->sidebar_color == 'yellow' ? 'btn-dark' : 'btn-warning'); ?>">Cancel</a>
                                    <?php endif; ?>

                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
    .table th {
        width: 200px;
    }
    .badge {
        padding: 8px 12px;
        font-size: 14px;
    }
</style>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('admin.admin_master', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH D:\Development\FGS\resources\views/admin/application/initiate/show.blade.php ENDPATH**/ ?>