<?php
$prefix = Request::route()->getPrefix();
$route = Route::current()->getName();

?>
<?php if(auth()->guard()->check()): ?>
<aside class="main-sidebar sidebar-<?php echo e(auth()->user()->sidebar_theam); ?>-<?php echo e(auth()->user()->sidebar_color); ?> elevation-4">
    <?php endif; ?>
    <!-- Brand Logo -->
    <a href="" class="brand-link">
        <img src="<?php echo e(asset('backend/dist/img/top.png')); ?>" alt="AdminLTE Logo" class="brand-image img-circle elevation-3"
            style="opacity: .8">
        <span class="brand-text font-weight-light">
            <h4><b>FGS</b> MIS</h4>
            
    </a>

    <!-- Sidebar -->
    <div class="sidebar">
        <!-- Sidebar user panel (optional) -->
        <div class="user-panel mt-2 pb-1 mb-1 d-flex">
            <div class="image">

                <img src="<?php echo e(asset('backend/dist/img/profile.jpg')); ?>" class="" alt="User Image">

            </div>
            <div class="info">
                <a href="" class="d-block">
                    <?php if(auth()->guard()->check()): ?>
                    <?php echo e(strtoupper(auth()->user()->name)); ?>

                    <?php endif; ?>
                    <br>
                    <span class="" style="font-size: 12px">
                        <i class="nav-icon far fa-circle text-success"></i>&nbsp; Online</span>
                </a>
                </a>
            </div>
        </div>

        <!-- SidebarSearch Form -->
        

        <!-- Sidebar Menu -->
        <nav class="mt-2">
            <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu"
                data-accordion="false">
                <!-- Add icons to the links using the .nav-icon class
               with font-awesome or any other icon font library -->
                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('dashbord.view')): ?>
                <li class="nav-item menu-open">
                    <a href="<?php echo e(route('dashboard')); ?>" class="nav-link <?php echo e($route == 'dashboard' ? 'active' : ''); ?>">
                        <i class="nav-icon fas fa-tachometer-alt"></i>
                        <p>
                            Dashboard
                        </p>
                    </a>

                </li>
                <?php endif; ?>

                <?php if (\Illuminate\Support\Facades\Blade::check('role', 'Super-Admin|Admin|FGS-Admin|Admin-Officer|Subject-Clerk|Subject-Assistant')): ?>
                <li class="nav-item">
                    <a href="#" class="nav-link <?php echo e($prefix == '/profile' ? 'active' : ''); ?>">
                        <i class="nav-icon fas fa-user-plus"></i>
                        <p>
                            Manage Profile
                            <i class="fas fa-angle-left right"></i>
                            
                        </p>
                    </a>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('perofile.view')): ?>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="<?php echo e(route('profile.view')); ?>"
                                class="nav-link <?php echo e($route == 'profile.view' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Your Profile</p>
                            </a>
                        </li>
                    </ul>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="<?php echo e(route('change.password.view')); ?>"
                                class="nav-link <?php echo e($route == 'change.password.view' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Change Password</p>
                            </a>
                        </li>
                    </ul>
                    <?php endif; ?>
                </li>
                <?php endif; ?>

                <?php if (\Illuminate\Support\Facades\Blade::check('role', 'Super-Admin|Admin|Admin-Officer|FGS-Admin')): ?>
                <li class="nav-item">
                    <a href="#" class="nav-link <?php echo e($prefix == '/user' ? 'active' : ''); ?>">
                        <i class="nav-icon fas fa-users"></i>
                        <p>
                            Manage Users
                            <i class="fas fa-angle-left right"></i>
                            
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('user.view')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('user.view')); ?>"
                                class="nav-link <?php echo e($route == 'user.view' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>View Users</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('user.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('user.add.view')); ?>"
                                class="nav-link <?php echo e($route == 'user.add.view' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Add New User</p>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>
                <?php if (\Illuminate\Support\Facades\Blade::check('role', 'Super-Admin|Admin')): ?>
                <li class="nav-item">
                    <a href="#" class="nav-link <?php echo e($prefix == '/role' ? 'active' : ''); ?>">
                        <i class="nav-icon fa fa-id-badge"></i>
                        <p>
                            Role
                            <i class="fas fa-angle-left right"></i>
                            
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('role.index')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('role.index')); ?>"
                                class="nav-link <?php echo e($route == 'role.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Role List</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('role.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('role.add')); ?>"
                                class="nav-link <?php echo e($route == 'role.add' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Role Add</p>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>
                <?php if (\Illuminate\Support\Facades\Blade::check('role', 'Super-Admin|Admin')): ?>
                <li class="nav-item">
                    <a href="#" class="nav-link <?php echo e($prefix == '/permission' ? 'active' : ''); ?>">
                        <i class="nav-icon fa fa-lock"></i>
                        <p>
                            Permission
                            <i class="fas fa-angle-left right"></i>
                            
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('permission.index')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('permission.index')); ?>"
                                class="nav-link <?php echo e($route == 'permission.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Permission List</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('permission.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('permission.add')); ?>"
                                class="nav-link <?php echo e($route == 'permission.add' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Permission Add</p>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>

                <?php if (\Illuminate\Support\Facades\Blade::check('role', 'Super-Admin|Admin|FGS-Admin|Admin-Officer|Subject-Clerk|Subject-Assistant')): ?>
                <li class="nav-item">
                    <a href="#" class="nav-link <?php echo e($prefix == '/setup' ? 'active' : ''); ?>">
                        <i class="nav-icon fa fa-cogs"></i>
                        <p>
                            Setup Management
                            <i class="fas fa-angle-left right"></i>
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('category.type.list')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('category.type.index')); ?>"
                                class="nav-link <?php echo e($route == 'category.type.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Category Type</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('category.list')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('category.index')); ?>"
                                class="nav-link <?php echo e($route == 'category.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Category </p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('income.type.list')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('income.type.index')); ?>"
                                class="nav-link <?php echo e($route == 'income.type.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Income Type</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('study.board.list')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('study.board.index')); ?>"
                                class="nav-link <?php echo e($route == 'study.board.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Study Board </p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('study.board.subject.list')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('study.board.subject.index')); ?>"
                                class="nav-link <?php echo e($route == 'study.board.subject.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Study Board Subject Area</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('study.board.chair.person.list')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('study.board.chair.person.index')); ?>"
                                class="nav-link <?php echo e($route == 'study.board.chair.person.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Study Board Chair</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('course.list')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('course.index')); ?>"
                                class="nav-link <?php echo e($route == 'course.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Course</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('course.fee.list')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('course.fee.index')); ?>"
                                class="nav-link <?php echo e($route == 'course.fee.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Taught Course Fee</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('course.research.fee.list')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('course.research.fee.index')); ?>"
                                class="nav-link <?php echo e($route == 'course.research.fee.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Research Course Fee</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('course.coordinator.list')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('course.coordinator.index')); ?>"
                                class="nav-link <?php echo e($route == 'course.coordinator.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Course Coordinator</p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('course.operator.list')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('course.operator.index')); ?>"
                                class="nav-link <?php echo e($route == 'course.operator.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Course Operator</p>
                            </a>
                        </li>
                        <?php endif; ?>

                    </ul>
                </li>
                <?php endif; ?>

                <?php if (\Illuminate\Support\Facades\Blade::check('role', 'Super-Admin|Admin|FGS-Admin|Admin-Officer')): ?>
                <li class="nav-item">
                    <a href="#" class="nav-link <?php echo e($prefix == '/application' ? 'active' : ''); ?>">
                        <i class="nav-icon fa fa-book"></i>
                        <p>
                            Application
                            <i class="fas fa-angle-left right"></i>
                            
                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('application.initiate.taught.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('application.initiate.taught.add')); ?>"
                                class="nav-link <?php echo e($route == 'application.initiate.taught.add' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Taught Course Initiate </p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('application.initiate.research.create')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('application.initiate.research.add')); ?>"
                                class="nav-link <?php echo e($route == 'application.initiate.research.add' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Research Course Initiate </p>
                            </a>
                        </li>
                        <?php endif; ?>
                        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('application.initiate.list')): ?>
                        <li class="nav-item">
                            <a href="<?php echo e(route('application.initiate.index')); ?>"
                                class="nav-link <?php echo e($route == 'application.initiate.index' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Course Open List</p>
                            </a>
                        </li>
                        <?php endif; ?>
                    </ul>
                </li>
                <?php endif; ?>
                <?php if (\Illuminate\Support\Facades\Blade::check('role', 'Super-Admin|Admin|FGS-Admin|Admin-Officer|Subject-Clerk|Subject-Assistant')): ?>
                <li class="nav-item">
                    <a href="#" class="nav-link <?php echo e($prefix == '/oldData' || $prefix == '/student' ? 'active' : ''); ?>">
                        <i class="nav-icon fa fa-graduation-cap"></i>
                        <p>
                            Student
                            <i class="fas fa-angle-left right"></i>

                        </p>
                    </a>
                    <ul class="nav nav-treeview">
                        <li class="nav-item">
                            <a href="<?php echo e(route('student.old.data.list.open')); ?>"
                                class="nav-link <?php echo e($route == 'student.old.data.list.open' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Transfer Old Data</p>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="<?php echo e(route('registered.student.list.open')); ?>"
                                class="nav-link <?php echo e($route == 'registered.student.list.open' ? 'active' : ''); ?>">
                                <i class="far fa-circle nav-icon"></i>
                                <p>Registered Student</p>
                            </a>
                        </li>
                    </ul>
                </li>
                <?php endif; ?>


                <li class="nav-header"></li>
                <li class="nav-item ">
                    <a type="button" class="nav-link bg-danger" data-toggle="modal"
                        data-target="#exampleModalCenter">
                        <i class="nav-icon fas fa-power-off text-white"></i>
                        <p class="text">Logout</p>
                    </a>
                </li>

            </ul>
        </nav>
        <!-- /.sidebar-menu -->
    </div>
    <!-- /.sidebar -->
</aside>
<div class="modal fade" id="exampleModalCenter" tabindex="-1" role="dialog"
    aria-labelledby="exampleModalCenterTitle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="exampleModalCenterTitle">User Logout</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <h6>Are you realy want to logout system?</h6>
            </div>
            <div class="modal-footer">
                <a type="button" class="btn btn-rounded btn-danger" data-dismiss="modal">Cancel</a>
                <form method="post" action="<?php echo e(route('logout')); ?>">
                    <?php echo csrf_field(); ?>
                    <button type="submit" class="btn btn-rounded btn-primary">Logout</button>
                </form>
            </div>
        </div>
    </div>
</div>
<?php /**PATH D:\Development\FGS\resources\views/admin/body/sidebar.blade.php ENDPATH**/ ?>