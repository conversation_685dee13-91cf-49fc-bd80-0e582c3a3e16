<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>" />
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <title>FGS | MIS</title>
  <script async src="https://www.googletagmanager.com/gtag/js?id=G-P8CTV55DXX"></script>
  <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-P8CTV55DXX');
  </script>
  <link rel="icon" href="<?php echo e(asset('backend/dist/img/logo.png')); ?>" type="image/icon type">

  <!-- Google Font: Source Sans Pro -->
  <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Source+Sans+Pro:300,400,400i,700&display=fallback">
  <!-- Font Awesome -->
  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/fontawesome-free/css/all.min.css')); ?>">
  <!-- Ionicons -->
  <link rel="stylesheet" href="https://code.ionicframework.com/ionicons/2.0.1/css/ionicons.min.css">
  <!-- Tempusdominus Bootstrap 4 -->
  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css')); ?>">
  <!-- iCheck -->
  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/icheck-bootstrap/icheck-bootstrap.min.css')); ?>">
  <!-- JQVMap -->
  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/jqvmap/jqvmap.min.css')); ?>">
  <!-- Theme style -->
  <link rel="stylesheet" href="<?php echo e(asset('backend/dist/css/adminlte.min.css')); ?>">
  <!-- overlayScrollbars -->
  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/overlayScrollbars/css/OverlayScrollbars.min.css')); ?>">
  <!-- Daterange picker -->
  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/daterangepicker/daterangepicker.css')); ?>">
  <!-- summernote -->
  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/summernote/summernote-bs4.min.css')); ?>">
  <!-- DataTables -->
  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/datatables-bs4/css/dataTables.bootstrap4.min.css')); ?>">
  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/datatables-responsive/css/responsive.bootstrap4.min.css')); ?>">
  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/datatables-buttons/css/buttons.bootstrap4.min.css')); ?>">
  <!--RosterMessage -->
  <link rel="stylesheet" type="text/css" href="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.css" >
  <!-- Select2 -->
  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/select2/css/select2.min.css')); ?>">
  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css')); ?>">
  <!-- Tempusdominus Bootstrap 4 -->

  <link rel="stylesheet" href="<?php echo e(asset('backend/plugins/tempusdominus-bootstrap-4/css/tempusdominus-bootstrap-4.min.css')); ?>">
  <style type="text/css">
    ::-webkit-scrollbar {
    width: 10px;
}

::-webkit-scrollbar-track {
    background-color: #ebebeb;
    -webkit-border-radius: 10px;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    -webkit-border-radius: 10px;
    border-radius: 10px;
    background: #6d6d6d;
}
.readOnlyInput {

  border: none;
  resize: none;
  overflow: hidden;
  outline: none;
  font-size: 14px;
  color: initial;
  background-color: white;
  cursor: text;
}

textarea[readonly] {background-color:white !important;}

.addstyletextarea{

 /* border: solid; */
 resize: both;
 overflow: visible;
 outline: inherit;
 font-size: 16px;

}

#tags-input {
  display: flex;
  flex-wrap: wrap;
  border: 1px solid #ccc;
  padding: 5px;
  border-radius: 4px;
}

#tag-container {
  display: flex;
  flex-wrap: wrap;
}

.tag {
  background-color: #f0f0f0;
  border-radius: 4px;
  padding: 5px 10px;
  margin: 5px;
  display: flex;
  align-items: center;
}

.tag span {
  margin-right: 5px;
}

.tag .remove-tag {
  cursor: pointer;
  color: #888;
}

.floating-action-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 999;
}

.submitButton1 {
  display: flex;
  justify-content: center; /* Center the icon horizontally */
  align-items: center; /* Center the icon vertically */
  border-radius: 10%;
  color: white;
  background-color: #990000;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.floating-action-container .submitButton1:hover {
  background-color: #990000;
}

.floating-action-container .submitButton1:visited {
  background-color: #ffc107;
}

.swal2-confirm {
    color: black !important; /* Set the desired color */
  }

  .bullet-list::before {
            content: "•"; /* Use any symbol or text you want as the bullet point */
            margin-right: 8px; /* Adjust the spacing between the bullet and the text */
        }
  .bottom-li {
    position: absolute;
    bottom: 0;
    right: 0;
    padding-bottom: 45px;
    padding-left: 8px;
    /* padding: 40px; */
    /* background-color: #990000; */
    color: white;
    text-align: left; /* Change this line to text-align: right; */
    font-size: 16px;
}

.swal2-icon {
    font-size: 11px; /* Adjust the size as needed */
}

.cke_notifications_area
    {
        display: none !important;
    }
  </style>
</head>
<body class="hold-transition sidebar-mini layout-fixed">
<div class="wrapper">

  <!-- Preloader -->
  
  <?php echo $__env->make('frontend.body.header', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
  <!-- Main Sidebar Container -->
  <?php echo $__env->make('frontend.body.sidebar', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>
  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <?php echo $__env->yieldContent('frontend'); ?>
  </div>
  <!-- /.content-wrapper -->

  <!-- Control Sidebar -->
  <aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
  </aside>
  <!-- /.control-sidebar -->
</div>
<!-- ./wrapper -->

<!-- jQuery -->
<script src="<?php echo e(asset('backend/plugins/jquery/jquery.min.js')); ?>"></script>
<!-- jQuery UI 1.11.4 -->
<script src="<?php echo e(asset('backend/plugins/jquery-ui/jquery-ui.min.js')); ?>"></script>
<!-- Resolve conflict in jQuery UI tooltip with Bootstrap tooltip -->
<script>
  $.widget.bridge('uibutton', $.ui.button)
</script>
<!-- Bootstrap 4 -->
<script src="<?php echo e(asset('backend/plugins/bootstrap/js/bootstrap.bundle.min.js')); ?>"></script>
<!-- ChartJS -->
<script src="<?php echo e(asset('backend/plugins/chart.js/Chart.min.js')); ?>"></script>
<!-- Sparkline -->
<script src="<?php echo e(asset('backend/plugins/sparklines/sparkline.js')); ?>"></script>
<!-- JQVMap -->
<script src="<?php echo e(asset('backend/plugins/jqvmap/jquery.vmap.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/jqvmap/maps/jquery.vmap.usa.js')); ?>"></script>
<!-- jQuery Knob Chart -->
<script src="<?php echo e(asset('backend/plugins/jquery-knob/jquery.knob.min.js')); ?>"></script>
<!-- daterangepicker -->
<script src="<?php echo e(asset('backend/plugins/moment/moment.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/daterangepicker/daterangepicker.js')); ?>"></script>
<!-- Tempusdominus Bootstrap 4 -->
<script src="<?php echo e(asset('backend/plugins/tempusdominus-bootstrap-4/js/tempusdominus-bootstrap-4.min.js')); ?>"></script>
<!-- Summernote -->
<script src="<?php echo e(asset('backend/plugins/summernote/summernote-bs4.min.js')); ?>"></script>
<!-- overlayScrollbars -->
<script src="<?php echo e(asset('backend/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js')); ?>"></script>
<!-- AdminLTE App -->
<script src="<?php echo e(asset('backend/dist/js/adminlte.js')); ?>"></script>
<!-- AdminLTE for demo purposes -->
<script src="<?php echo e(asset('backend/dist/js/demo.js')); ?>"></script>
<!-- AdminLTE dashboard demo (This is only for demo purposes) -->
<script src="<?php echo e(asset('backend/dist/js/pages/dashboard.js')); ?>"></script>
<!-- DataTables  & Plugins -->
<script src="<?php echo e(asset('backend/plugins/datatables/jquery.dataTables.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/datatables-bs4/js/dataTables.bootstrap4.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/datatables-responsive/js/dataTables.responsive.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/datatables-responsive/js/responsive.bootstrap4.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/datatables-buttons/js/dataTables.buttons.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/datatables-buttons/js/buttons.bootstrap4.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/jszip/jszip.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/pdfmake/pdfmake.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/pdfmake/vfs_fonts.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/datatables-buttons/js/buttons.html5.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/datatables-buttons/js/buttons.print.min.js')); ?>"></script>
<script src="<?php echo e(asset('backend/plugins/datatables-buttons/js/buttons.colVis.min.js')); ?>"></script>
<!--RosterMessage -->
<script type="text/javascript" src="https://cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<!--SweetAlert -->
<script src="//cdn.jsdelivr.net/npm/sweetalert2@10"></script>

<!-- Select2 -->
<script src="<?php echo e(asset('backend/plugins/select2/js/select2.full.min.js')); ?>"></script>
<script src="//cdn.ckeditor.com/4.14.1/standard/ckeditor.js"></script>
<script type="text/javascript">
    $(document).ready(function () {
        $('.ckeditor').ckeditor({
            // CKEditor configuration options
            placeholder: 'Enter your content here...'
        });
    });

  $(function () {

    $(document).on('click','#delete',function(e){
           e.preventDefault();
           var link = $(this).attr("href");

           Swal.fire({
            title: 'Are you sure?',
            text: "Delete This Data!",
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#3085d6',
            cancelButtonColor: '#d33',
            confirmButtonText: 'Delete'
             }).then((result) => {
             if (result.isConfirmed) {
                window.location.href = link
                   Swal.fire(
                      'Deleting....',
                      '',
                      'info'
                    )
             }
          })
        });

    $("#example1").DataTable({
      "responsive": true,
      "lengthChange": false,
      "autoWidth": false,
      "ordering": false,
      "info": true,
      "stateSave": true,
      //"buttons": ["copy", "csv", "excel", "pdf", "print", "colvis"]
    }).buttons().container().appendTo('#example1_wrapper .col-md-6:eq(0)');


    $('#example2').DataTable({
      "paging": false,
      "lengthChange": false,
      "searching": true,
      "ordering": true,
      "info": true,
      "stateSave": true,
      "autoWidth": false,
      "responsive": true,
      "responsivePriority": 1 // Show "Action" column on small screens without the need to click "+"
    });

    if ($('#qualifications-table').length) {
        $('#qualifications-table').DataTable({
         "responsive": true,
         "lengthChange": true,
         "searching": false,
         "ordering": false,
         "info": false,
         "paging": false,
         "responsivePriority": 1
        });
    }

    if ($('#employee-record-table').length) {
        $('#employee-record-table').DataTable({
         "responsive": true,
         "lengthChange": true,
         "searching": false,
         "ordering": false,
         "info": false,
         "paging": false,
         "responsivePriority": 1
        });
    }

    $('.select2bs4').select2({
      theme: 'bootstrap4',
      tags: false
    });

    function matchStart(params, data) {
    params.term = params.term || '';
    if (data.text.toUpperCase().indexOf(params.term.toUpperCase()) == 0) {
        return data;
    }
    return false;
}


    $('.select2bs5').select2({
      theme: 'bootstrap4',
      tags: false,
      matcher: function(params, data) {
        return matchStart(params, data);
    },

    });

    $(document).on("select2:open", () => {
     document.querySelector(".select2-container--open .select2-search__field").focus()
    });

    //Date picker
    $('#date_of_birth').datetimepicker({
        format: 'DD-MMM-YYYY',
        viewMode: "years",
        minViewMode: "years",
        useCurrent: false,
    });

    //Date picker
    $('#proDateShow').datetimepicker({
        format: 'DD-MMM-YYYY',
        viewMode: "years",
        minViewMode: "years",
        useCurrent: false
    });

  });

  <?php if(Session::has('message')): ?>
 var type = "<?php echo e(Session::get('alert-type','info')); ?>"
 switch(type){
    case 'info':
    toastr.info(" <?php echo e(Session::get('message')); ?> ",{ fadeAway: 1000 });
    break;

    case 'success':
    toastr.success(" <?php echo e(Session::get('message')); ?> ",{ fadeAway: 1000 });
    break;

    case 'warning':
    toastr.warning(" <?php echo e(Session::get('message')); ?> ",{ fadeAway: 1000 });
    break;

    case 'error':
    toastr.error(" <?php echo e(Session::get('message')); ?> ",{ timeOut: 60000 });
    break;
 }
 <?php endif; ?>
</script>
</body>
</html>
<?php /**PATH D:\Development\FGS\resources\views/frontend/frontend_master.blade.php ENDPATH**/ ?>